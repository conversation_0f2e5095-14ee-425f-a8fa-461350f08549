<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية - الكويت</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#2563eb',
              secondary: '#3b82f6',
              accent: '#5b21b6',
              text: {
                light: '#f0f9ff',
                dark: '#1e293b'
              }
            },
            boxShadow: {
              card: '0 4px 20px rgba(0, 0, 0, 0.08)',
              button: '0 4px 12px rgba(37, 99, 235, 0.3)'
            }
          }
        }
      }
    </script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
        }
        
        /* Text animations */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(224, 231, 255, 0.5);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(96, 165, 250, 0.7);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.9);
        }
        
        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: #ffffff;
            margin: auto;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 600px;
            position: relative;
        }
        
        .close-button {
            color: #aaa;
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Dashboard cards */
        .dashboard-card {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        /* Print styling */
        @media print {
            body * {
                visibility: hidden;
            }
            .print-section, .print-section * {
                visibility: visible;
            }
            .print-section {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
        }
        
        /* Sidebar menu for mobile */
        @media (max-width: 768px) {
            #main-nav {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.5s ease;
            }
            
            #main-nav.active {
                max-height: 300px;
            }
        }
        
        /* Fade-in animations */
        .fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
        
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        .smooth-shadow {
            box-shadow: 0 10px 25px rgba(0,0,0,0.06);
        }
    </style>
    <!-- Firebase and HRMS functionality remains the same -->
</head>
<body class="min-h-screen flex flex-col">
    <!-- Header Section -->
    <header class="bg-gradient-to-r from-primary to-accent text-white p-4 sm:p-5 shadow-md rounded-b-xl">
        <div class="container mx-auto flex flex-col sm:flex-row justify-between items-stretch sm:items-center">
            <div class="flex justify-between items-center w-full sm:w-auto">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold flex items-center gap-2">
                        <i class="fas fa-user-friends"></i>
                        نظام إدارة الموارد البشرية
                    </h1>
                    <p class="text-xs sm:text-sm opacity-80 mt-1 flex items-center gap-1">
                        <i class="fas fa-map-marker-alt"></i> الكويت - أخصائي في إدارة الموارد البشرية
                    </p>
                </div>
                <button id="menu-toggle" class="sm:hidden block text-2xl">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav id="main-nav" class="mt-3 sm:mt-0 sm:block">
                <ul class="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 md:gap-5 text-sm sm:text-base">
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-dashboard" data-tab="dashboard-section" class="flex items-center gap-2">
                            <i class="fas fa-home w-5 text-center"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-employees" data-tab="employee-section" class="flex items-center gap-2">
                            <i class="fas fa-users w-5 text-center"></i>
                            الموظفون
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-attendance" data-tab="attendance-section" class="flex items-center gap-2">
                            <i class="fas fa-clock w-5 text-center"></i>
                            الحضور
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-leaves" data-tab="leaves-section" class="flex items-center gap-2">
                            <i class="fas fa-calendar-alt w-5 text-center"></i>
                            الإجازات
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-salaries" data-tab="salaries-section" class="flex items-center gap-2">
                            <i class="fas fa-wallet w-5 text-center"></i>
                            الرواتب
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-documents" data-tab="documents-section" class="flex items-center gap-2">
                            <i class="fas fa-folder w-5 text-center"></i>
                            الوثائق
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-performance" data-tab="performance-section" class="flex items-center gap-2">
                            <i class="fas fa-chart-bar w-5 text-center"></i>
                            الأداء
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-reports" data-tab="reports-section" class="flex items-center gap-2">
                            <i class="fas fa-file-chart-line w-5 text-center"></i>
                            التقارير
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="container mx-auto p-4 sm:p-6 flex-grow">
        <div id="message-box" class="hidden"></div>
        
        <!-- User info and quick stats bar -->
        <div class="flex flex-wrap items-center justify-between gap-4 mb-6 bg-white p-4 rounded-xl shadow-sm">
            <div class="flex-1">
                <div class="flex items-center gap-3">
                    <div class="bg-gradient-to-r from-primary to-accent p-2 rounded-full">
                        <i class="fas fa-user-circle text-white text-xl"></i>
                    </div>
                    <div>
                        <p id="user-id-display" class="text-sm text-gray-600"></p>
                        <p class="font-bold">مرحبا بك في نظام إدارة الموارد البشرية</p>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap gap-2">
                <div class="flex items-center gap-2 bg-blue-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-users text-blue-600"></i>
                    <span class="font-bold text-blue-700">الموظفين: <span id="total-employees-count">0</span></span>
                </div>
                <div class="flex items-center gap-2 bg-yellow-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-clock text-yellow-600"></i>
                    <span class="font-bold text-yellow-700">الحضور: <span id="attendance-count">0</span></span>
                </div>
                <div class="flex items-center gap-2 bg-green-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-calendar-alt text-green-600"></i>
                    <span class="font-bold text-green-700">الإجازات: <span id="leaves-count">0</span></span>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 active fade-in">
            <div class="flex justify-between items-center mb-6 border-b-2 pb-4 border-primary">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">لوحة التحكم</h2>
                <div class="text-sm text-gray-500 hidden sm:block">
                    <i class="far fa-calendar-alt ml-2"></i>
                    <span id="current-date">اليوم: 20 ديسمبر 2023</span>
                </div>
            </div>
            
            <!-- Dashboard Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
                <!-- Card: Total Employees -->
                <div class="dashboard-card bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-users text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold">254</p>
                    <p class="text-lg mt-2">إجمالي الموظفين</p>
                    <p class="flex items-center gap-1 mt-2 text-blue-100">
                        <i class="fas fa-arrow-up text-xs"></i>
                        +12 هذا الشهر
                    </p>
                </div>
                
                <!-- Card: Active Employees -->
                <div class="dashboard-card bg-gradient-to-br from-green-500 to-green-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-user-check text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold">224</p>
                    <p class="text-lg mt-2">الموظفين النشطين</p>
                    <p class="flex items-center gap-1 mt-2 text-green-100">
                        94% حضور
                    </p>
                </div>
                
                <!-- Card: Employees on Leave -->
                <div class="dashboard-card bg-gradient-to-br from-yellow-500 to-yellow-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-calendar-alt text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold" id="employees-on-leave-count">22</p>
                    <p class="text-lg mt-2">الموظفون في إجازة</p>
                    <p class="flex items-center gap-1 mt-2 text-yellow-100">
                        +5 هذا الأسبوع
                    </p>
                </div>
                
                <!-- Card: Upcoming Hires -->
                <div class="dashboard-card bg-gradient-to-br from-violet-500 to-violet-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-user-plus text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold" id="upcoming-hires-count">8</p>
                    <p class="text-lg mt-2">توظيفات قادمة</p>
                    <p class="flex items-center gap-1 mt-2 text-violet-100">
                        خلال 3 أشهر
                    </p>
                </div>
            </div>
            
            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Department Distribution Chart -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="border-b border-gray-200 px-5 py-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-chart-pie text-blue-500 mr-2"></i>
                            توزيع الموظفين حسب الأقسام
                        </h3>
                    </div>
                    <div class="p-5">
                        <canvas id="departmentChart" width="400" height="300"></canvas>
                    </div>
                </div>

                <!-- Attendance Trend Chart -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="border-b border-gray-200 px-5 py-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-chart-line text-green-500 mr-2"></i>
                            اتجاه الحضور الأسبوعي
                        </h3>
                    </div>
                    <div class="p-5">
                        <canvas id="attendanceChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div class="border-b border-gray-200">
                    <h3 class="px-5 py-4 text-xl font-semibold text-gray-800">
                        <i class="fas fa-bell text-yellow-500 mr-2"></i>
                        التنبيهات والملاحظات المهمة
                    </h3>
                </div>
                <div id="expiration-notifications" class="p-5 space-y-4">
                    <!-- Dynamic notifications will be loaded here -->
                </div>
            </div>
        </div>
        
        <!-- Employee Section -->
        <div id="employee-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-blue-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">المعلومات الشخصية للموظفين</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-employee-btn" class="bg-primary hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-user-plus"></i>
                        إضافة موظف جديد
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-filter"></i>
                        تصفية الموظفين
                    </button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-12 gap-5 mb-6">
                <!-- Search Section -->
                <div class="md:col-span-8 flex">
                    <input type="text" id="employee-search" placeholder="بحث الموظفين بالاسم أو الرقم" class="flex-grow border-l-0 rounded-l-lg border border-gray-300 px-4 py-2 focus:outline-none focus:border-primary">
                    <button id="search-btn" class="bg-primary text-white px-5 rounded-r-lg hover:bg-blue-700 transition-all">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <!-- Actions Section -->
                <div class="md:col-span-4 flex gap-2 justify-end">
                    <button id="print-employees-btn" class="bg-white border border-gray-300 rounded-lg px-4 h-full hover:bg-gray-50 flex items-center gap-2 transition-all">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button id="export-employees-btn" class="bg-white border border-gray-300 rounded-lg px-4 h-full hover:bg-gray-50 flex items-center gap-2 transition-all">
                        <i class="fas fa-file-export"></i>
                        تصدير
                    </button>
                </div>
            </div>
            
            <!-- Employee List -->
            <div id="employee-list" class="space-y-4">
                <!-- Employee Card 1 -->
                <div class="employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md">
                    <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                        <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                    </div>
                    <div class="p-4 sm:p-6 flex-grow">
                        <div class="flex flex-wrap justify-between">
                            <h3 class="text-xl font-bold text-gray-800">طارق عبدالله خالد</h3>
                            <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">نشيط</span>
                        </div>
                        
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-id-card text-gray-400 w-5"></i>
                                <span class="font-medium">الرقم المدني: 293012301230</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                                <span>+965 50012345</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-building text-gray-400 w-5"></i>
                                <span>الأقسام: تقنية المعلومات</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-briefcase text-gray-400 w-5"></i>
                                <span>المسمى الوظيفي: مطور ويب</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-4">
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-user-circle text-blue-600"></i>
                                عرض الملف الشخصي
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-envelope text-yellow-600"></i>
                                إرسال رسالة
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-money-bill-wave text-green-600"></i>
                                الرواتب
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Card 2 -->
                <div class="employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md">
                    <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                        <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                    </div>
                    <div class="p-4 sm:p-6 flex-grow">
                        <div class="flex flex-wrap justify-between">
                            <h3 class="text-xl font-bold text-gray-800">نورة أحمد سعد</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">في إجازة</span>
                        </div>
                        
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-id-card text-gray-400 w-5"></i>
                                <span class="font-medium">الرقم المدني: 293082301230</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                                <span>+965 50054321</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-building text-gray-400 w-5"></i>
                                <span>الأقسام: الموارد البشرية</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-briefcase text-gray-400 w-5"></i>
                                <span>المسمى الوظيفي: أخصائي شؤون الموظفين</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-4">
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-user-circle text-blue-600"></i>
                                عرض الملف الشخصي
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-envelope text-yellow-600"></i>
                                إرسال رسالة
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-calendar-alt text-purple-600"></i>
                                حالة الإجازة
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Loading indicator -->
                <p class="text-gray-600 text-center text-lg py-8 animate-pulse italic">
                    جاري تحميل بيانات الموظفين...
                </p>
            </div>
        </div>
        
        <!-- Attendance Section -->
        <div id="attendance-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800 mb-6 border-b-2 pb-4 border-primary">الحضور والانصراف</h2>
            
            <!-- Attendance Today Stats -->
            <div class="flex flex-col md:flex-row gap-5 mb-8">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-xl w-full md:w-1/2">
                    <div class="flex justify-between">
                        <div>
                            <p class="text-sm opacity-90">حضور اليوم</p>
                            <p class="text-3xl font-bold mt-1">189 <span class="text-lg font-medium">/ 230</span></p>
                            <p class="text-sm mt-2 flex items-center gap-1">89% الحضور</p>
                        </div>
                        <div class="bg-white/20 p-4 rounded-full">
                            <i class="fas fa-user-clock text-3xl"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="h-2 bg-white/40 rounded-full">
                            <div class="h-2 bg-white rounded-full" style="width: 89%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 border rounded-xl p-6 w-full md:w-1/2">
                    <p class="text-lg font-semibold mb-3">تسجيل الدخول السريع</p>
                    <div class="bg-blue-50 border border-blue-100 rounded-xl p-4">
                        <div class="flex flex-col sm:flex-row sm:items-center gap-3 mb-3">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">اختر الموظف:</label>
                                <select id="attendance-employee-select" class="w-full border rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary/50">
                                    <option>-- اختر الموظف --</option>
                                    <option>طارق عبدالله خالد</option>
                                    <option>نورة أحمد سعد</option>
                                    <option>فهد عبدالعزيز محمد</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button id="clock-in-out-btn" class="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-600 text-white font-medium py-2 px-5 rounded-lg transition duration-300 shadow-md w-full sm:w-auto">
                                <i class="fas fa-clock"></i>
                                تسجيل الدخول / الخروج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Records -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">سجلات الحضور</h3>
                    <div>
                        <select id="month-select" class="text-sm border p-2 rounded">
                            <option>ديسمبر 2023</option>
                            <option selected>نوفمبر 2023</option>
                            <option>أكتوبر 2023</option>
                        </select>
                    </div>
                </div>
                <div id="attendance-list" class="p-4 sm:p-5">
                    <ul class="divide-y divide-gray-100">
                        <li class="py-4">
                            <div class="flex flex-col sm:flex-row sm:items-center">
                                <div class="flex-1 mb-3 sm:mb-0">
                                    <h4 class="font-medium text-gray-800">طارق عبدالله خالد</h4>
                                    <p class="text-sm text-gray-500 mt-1">الأقسام: تقنية المعلومات</p>
                                </div>
                                <div class="flex items-center gap-4 sm:gap-8">
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الدخول</p>
                                        <p class="font-medium text-green-600 mt-0.5">08:45 صباحًا</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الخروج</p>
                                        <p class="font-medium text-red-600 mt-0.5">05:12 مساءً</p>
                                    </div>
                                    <div class="bg-blue-50 px-3 py-1 rounded-full">
                                        <span class="text-blue-600 text-sm">8 ساعات 27 د</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        <li class="py-4">
                            <div class="flex flex-col sm:flex-row sm:items-center">
                                <div class="flex-1 mb-3 sm:mb-0">
                                    <h4 class="font-medium text-gray-800">نورة أحمد سعد</h4>
                                    <p class="text-sm text-gray-500 mt-1">الأقسام: الموارد البشرية</p>
                                </div>
                                <div class="flex items-center gap-4 sm:gap-8">
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الدخول</p>
                                        <p class="font-medium text-green-600 mt-0.5">08:32 صباحًا</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الخروج</p>
                                        <p class="font-medium text-gray-400 mt-0.5">--</p>
                                    </div>
                                    <div class="bg-yellow-50 px-3 py-1 rounded-full">
                                        <span class="text-yellow-600 text-sm">لا تزال موجودة</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <p class="text-gray-600 text-center py-5 italic">
                        جارٍ تحميل المزيد من سجلات الحضور...
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Leaves Section -->
        <div id="leaves-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-green-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">إدارة الإجازات</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-leave-btn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-calendar-plus"></i>
                        طلب إجازة جديد
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-filter"></i>
                        تصفية الإجازات
                    </button>
                </div>
            </div>

            <!-- Leave Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-5 mb-8">
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-calendar-check text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">45</p>
                    <p class="text-sm mt-1">إجازات معتمدة</p>
                </div>
                <div class="bg-gradient-to-br from-yellow-500 to-yellow-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">12</p>
                    <p class="text-sm mt-1">في انتظار الموافقة</p>
                </div>
                <div class="bg-gradient-to-br from-red-500 to-red-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-times-circle text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">3</p>
                    <p class="text-sm mt-1">إجازات مرفوضة</p>
                </div>
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-user-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">22</p>
                    <p class="text-sm mt-1">موظفون في إجازة</p>
                </div>
            </div>

            <!-- Leave Requests List -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4">
                    <h3 class="text-lg font-semibold text-gray-800">طلبات الإجازات</h3>
                </div>
                <div id="leaves-list" class="p-4 sm:p-5">
                    <div class="space-y-4">
                        <!-- Leave Request 1 -->
                        <div class="border border-gray-100 rounded-xl p-4 hover:shadow-md transition-all">
                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">أحمد محمد علي</h4>
                                    <p class="text-sm text-gray-500 mt-1">إجازة سنوية - 5 أيام</p>
                                    <p class="text-sm text-gray-500">من 2024-01-15 إلى 2024-01-19</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">في انتظار الموافقة</span>
                                    <div class="flex gap-2">
                                        <button class="bg-green-100 hover:bg-green-200 text-green-700 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-check"></i> موافقة
                                        </button>
                                        <button class="bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-times"></i> رفض
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Leave Request 2 -->
                        <div class="border border-gray-100 rounded-xl p-4 hover:shadow-md transition-all">
                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">فاطمة سعد الدين</h4>
                                    <p class="text-sm text-gray-500 mt-1">إجازة مرضية - 3 أيام</p>
                                    <p class="text-sm text-gray-500">من 2024-01-10 إلى 2024-01-12</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">معتمدة</span>
                                    <div class="flex gap-2">
                                        <button class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p class="text-gray-600 text-center py-5 italic">
                        جارٍ تحميل المزيد من طلبات الإجازات...
                    </p>
                </div>
            </div>
        </div>

        <!-- Salaries Section -->
        <div id="salaries-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-purple-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">إدارة الرواتب</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-salary-btn" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        إضافة راتب
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-calculator"></i>
                        حساب الرواتب
                    </button>
                </div>
            </div>

            <!-- Salary Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-5 mb-8">
                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-money-bill-wave text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">125,000</p>
                    <p class="text-sm mt-1">إجمالي الرواتب (د.ك)</p>
                </div>
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">198</p>
                    <p class="text-sm mt-1">رواتب مدفوعة</p>
                </div>
                <div class="bg-gradient-to-br from-orange-500 to-orange-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">32</p>
                    <p class="text-sm mt-1">رواتب معلقة</p>
                </div>
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">542</p>
                    <p class="text-sm mt-1">متوسط الراتب (د.ك)</p>
                </div>
            </div>

            <!-- Salary List -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">كشف الرواتب</h3>
                    <div class="flex gap-2">
                        <select class="text-sm border p-2 rounded">
                            <option>يناير 2024</option>
                            <option selected>ديسمبر 2023</option>
                            <option>نوفمبر 2023</option>
                        </select>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القسم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الراتب الأساسي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البدلات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخصومات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصافي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300"></div>
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">طارق عبدالله خالد</div>
                                            <div class="text-sm text-gray-500">293012301230</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">تقنية المعلومات</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">650 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">150 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">50 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">750 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        مدفوع
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300"></div>
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">نورة أحمد سعد</div>
                                            <div class="text-sm text-gray-500">293082301230</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الموارد البشرية</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">580 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">120 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">35 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">665 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        معلق
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-5 py-4 text-center">
                    <p class="text-gray-600 italic">
                        جارٍ تحميل المزيد من بيانات الرواتب...
                    </p>
                </div>
            </div>
        </div>

        <!-- Documents Section -->
        <div id="documents-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-indigo-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">إدارة الوثائق</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="upload-document-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-upload"></i>
                        رفع وثيقة جديدة
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-search"></i>
                        البحث في الوثائق
                    </button>
                </div>
            </div>

            <!-- Document Categories -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-4 rounded-xl text-center cursor-pointer hover:shadow-lg transition-all" onclick="filterDocuments('passport')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-passport text-2xl"></i>
                    </div>
                    <p class="font-medium">جوازات السفر</p>
                    <p class="text-sm opacity-80" id="passport-count">3 وثائق</p>
                </div>

                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-4 rounded-xl text-center cursor-pointer hover:shadow-lg transition-all" onclick="filterDocuments('residence')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-id-card text-2xl"></i>
                    </div>
                    <p class="font-medium">الإقامات</p>
                    <p class="text-sm opacity-80" id="residence-count">3 وثائق</p>
                </div>

                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-4 rounded-xl text-center cursor-pointer hover:shadow-lg transition-all" onclick="filterDocuments('license')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-briefcase text-2xl"></i>
                    </div>
                    <p class="font-medium">رخص العمل</p>
                    <p class="text-sm opacity-80" id="license-count">3 وثائق</p>
                </div>

                <div class="bg-gradient-to-br from-orange-500 to-orange-600 text-white p-4 rounded-xl text-center cursor-pointer hover:shadow-lg transition-all" onclick="filterDocuments('other')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-file-alt text-2xl"></i>
                    </div>
                    <p class="font-medium">وثائق أخرى</p>
                    <p class="text-sm opacity-80" id="other-count">2 وثائق</p>
                </div>
            </div>

            <!-- Documents List -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">الوثائق المرفوعة</h3>
                    <div class="flex gap-2">
                        <select id="document-filter" class="text-sm border p-2 rounded">
                            <option value="all">جميع الوثائق</option>
                            <option value="passport">جوازات السفر</option>
                            <option value="residence">الإقامات</option>
                            <option value="license">رخص العمل</option>
                            <option value="health">البطاقات الصحية</option>
                            <option value="other">أخرى</option>
                        </select>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-download"></i> تحميل الكل
                        </button>
                    </div>
                </div>
                <div id="documents-list" class="p-4 sm:p-5">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Sample Document Cards -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-passport text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">جواز سفر</h4>
                                        <p class="text-sm text-gray-500">طارق عبدالله خالد</p>
                                    </div>
                                </div>
                                <div class="flex gap-1">
                                    <button class="text-gray-400 hover:text-blue-600 transition-colors" title="عرض">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-green-600 transition-colors" title="تحميل">
                                        <i class="fas fa-download text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-red-600 transition-colors" title="حذف">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>تاريخ الرفع: 2024-01-15</p>
                                <p>الحجم: 2.3 MB • النوع: PDF</p>
                                <div class="mt-2">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">صالح</span>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-id-card text-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">الإقامة</h4>
                                        <p class="text-sm text-gray-500">نورة أحمد سعد</p>
                                    </div>
                                </div>
                                <div class="flex gap-1">
                                    <button class="text-gray-400 hover:text-blue-600 transition-colors" title="عرض">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-green-600 transition-colors" title="تحميل">
                                        <i class="fas fa-download text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-red-600 transition-colors" title="حذف">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>تاريخ الرفع: 2024-01-12</p>
                                <p>الحجم: 1.8 MB • النوع: PDF</p>
                                <div class="mt-2">
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">ينتهي قريباً</span>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-briefcase text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">رخصة عمل</h4>
                                        <p class="text-sm text-gray-500">فهد عبدالعزيز محمد</p>
                                    </div>
                                </div>
                                <div class="flex gap-1">
                                    <button class="text-gray-400 hover:text-blue-600 transition-colors" title="عرض">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-green-600 transition-colors" title="تحميل">
                                        <i class="fas fa-download text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-red-600 transition-colors" title="حذف">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>تاريخ الرفع: 2024-01-10</p>
                                <p>الحجم: 1.5 MB • النوع: PDF</p>
                                <div class="mt-2">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">صالح</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center py-8 mt-6">
                        <div class="bg-gray-50 rounded-xl p-8">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500 mb-4">اسحب الملفات هنا أو اضغط لرفع وثائق جديدة</p>
                            <button onclick="document.getElementById('upload-document-modal').style.display='flex'" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-all">
                                <i class="fas fa-upload mr-2"></i>
                                رفع وثيقة جديدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Section -->
        <div id="performance-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-pink-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">تقييم الأداء</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-evaluation-btn" class="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        تقييم جديد
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-chart-line"></i>
                        تقرير الأداء
                    </button>
                </div>
            </div>

            <!-- Performance Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-5 mb-8">
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-star text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">4.2</p>
                    <p class="text-sm mt-1">متوسط التقييم</p>
                </div>

                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-trophy text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">85%</p>
                    <p class="text-sm mt-1">تحقيق الأهداف</p>
                </div>

                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">156</p>
                    <p class="text-sm mt-1">تقييمات مكتملة</p>
                </div>

                <div class="bg-gradient-to-br from-orange-500 to-orange-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">12</p>
                    <p class="text-sm mt-1">تقييمات معلقة</p>
                </div>
            </div>

            <!-- Performance Chart -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden mb-8">
                <div class="border-b border-gray-200 px-5 py-4">
                    <h3 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-chart-area text-pink-500 mr-2"></i>
                        اتجاه الأداء الشهري
                    </h3>
                </div>
                <div class="p-5">
                    <canvas id="performanceChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Employee Performance List -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">تقييمات الموظفين</h3>
                    <div class="flex gap-2">
                        <select class="text-sm border p-2 rounded">
                            <option>جميع الموظفين</option>
                            <option>تقييمات ممتازة</option>
                            <option>تقييمات جيدة</option>
                            <option>تحتاج تحسين</option>
                        </select>
                        <button class="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-download"></i> تصدير التقييمات
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القسم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التقييم العام</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأهداف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر تقييم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300"></div>
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">طارق عبدالله خالد</div>
                                            <div class="text-sm text-gray-500">مطور ويب</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">تقنية المعلومات</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                        <span class="mr-2 text-sm font-medium text-gray-900">4.2</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8/10</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        ممتاز
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-blue-600 hover:text-blue-900" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-600 hover:text-green-900" title="تقييم جديد">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="text-purple-600 hover:text-purple-900" title="تقرير الأداء">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300"></div>
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">نورة أحمد سعد</div>
                                            <div class="text-sm text-gray-500">أخصائي شؤون الموظفين</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الموارد البشرية</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="mr-2 text-sm font-medium text-gray-900">4.8</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">9/10</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-10</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        ممتاز
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-blue-600 hover:text-blue-900" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-600 hover:text-green-900" title="تقييم جديد">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="text-purple-600 hover:text-purple-900" title="تقرير الأداء">
                                            <i class="fas fa-chart-line"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-5 py-4 text-center">
                    <p class="text-gray-600 italic">
                        جارٍ تحميل المزيد من تقييمات الأداء...
                    </p>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reports-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-teal-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">التقارير والإحصائيات</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="generate-report-btn" class="bg-teal-600 hover:bg-teal-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-file-alt"></i>
                        إنشاء تقرير
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-calendar"></i>
                        تقارير مجدولة
                    </button>
                </div>
            </div>

            <!-- Report Categories -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl cursor-pointer hover:shadow-lg transition-all" onclick="generateReport('employees')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-lg mb-2">تقرير الموظفين</h3>
                    <p class="text-sm opacity-90">بيانات شاملة عن جميع الموظفين</p>
                </div>

                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl cursor-pointer hover:shadow-lg transition-all" onclick="generateReport('attendance')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-lg mb-2">تقرير الحضور</h3>
                    <p class="text-sm opacity-90">إحصائيات الحضور والانصراف</p>
                </div>

                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-5 rounded-xl cursor-pointer hover:shadow-lg transition-all" onclick="generateReport('leaves')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-calendar-alt text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-lg mb-2">تقرير الإجازات</h3>
                    <p class="text-sm opacity-90">تفاصيل الإجازات والأرصدة</p>
                </div>

                <div class="bg-gradient-to-br from-orange-500 to-orange-600 text-white p-5 rounded-xl cursor-pointer hover:shadow-lg transition-all" onclick="generateReport('performance')">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-chart-bar text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-lg mb-2">تقرير الأداء</h3>
                    <p class="text-sm opacity-90">تقييمات الأداء والإنجازات</p>
                </div>
            </div>

            <!-- Quick Stats Dashboard -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden mb-8">
                <div class="border-b border-gray-200 px-5 py-4">
                    <h3 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-tachometer-alt text-teal-500 mr-2"></i>
                        لوحة الإحصائيات السريعة
                    </h3>
                </div>
                <div class="p-5">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600" id="total-employees-stat">0</div>
                            <div class="text-sm text-gray-600">إجمالي الموظفين</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600" id="present-today-stat">0</div>
                            <div class="text-sm text-gray-600">حاضر اليوم</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600" id="on-leave-stat">0</div>
                            <div class="text-sm text-gray-600">في إجازة</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600" id="avg-performance-stat">0</div>
                            <div class="text-sm text-gray-600">متوسط الأداء</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">التقارير الحديثة</h3>
                    <button class="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg text-sm">
                        <i class="fas fa-plus"></i> تقرير جديد
                    </button>
                </div>
                <div class="p-5">
                    <div class="space-y-4">
                        <!-- Sample Report Item -->
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">تقرير الموظفين الشهري</h4>
                                    <p class="text-sm text-gray-500">تم إنشاؤه في 2024-01-15 • 254 موظف</p>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button class="text-gray-400 hover:text-blue-600 transition-colors" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-gray-400 hover:text-green-600 transition-colors" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-600 transition-colors" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-clock text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">تقرير الحضور الأسبوعي</h4>
                                    <p class="text-sm text-gray-500">تم إنشاؤه في 2024-01-12 • معدل الحضور 89%</p>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button class="text-gray-400 hover:text-blue-600 transition-colors" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-gray-400 hover:text-green-600 transition-colors" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-600 transition-colors" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-bar text-purple-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">تقرير الأداء الربعي</h4>
                                    <p class="text-sm text-gray-500">تم إنشاؤه في 2024-01-10 • متوسط التقييم 4.2</p>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button class="text-gray-400 hover:text-blue-600 transition-colors" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-gray-400 hover:text-green-600 transition-colors" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-600 transition-colors" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="text-center py-8 mt-6">
                        <div class="bg-gray-50 rounded-xl p-8">
                            <i class="fas fa-chart-line text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500 mb-4">إنشاء تقارير مخصصة حسب احتياجاتك</p>
                            <button onclick="document.getElementById('generate-report-modal').style.display='flex'" class="bg-teal-600 text-white px-6 py-3 rounded-lg hover:bg-teal-700 transition-all">
                                <i class="fas fa-plus mr-2"></i>
                                إنشاء تقرير مخصص
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add Employee Modal -->
    <div id="add-employee-modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 id="form-title" class="text-2xl font-bold text-gray-800 mb-6">إضافة موظف جديد</h2>

            <form id="employee-form" class="space-y-4">
                <input type="hidden" id="employee-id-field" name="employee-id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="first-name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الأول</label>
                        <input type="text" id="first-name" name="first-name" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="last-name" class="block text-sm font-medium text-gray-700 mb-1">اسم العائلة</label>
                        <input type="text" id="last-name" name="last-name" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="civil-id" class="block text-sm font-medium text-gray-700 mb-1">الرقم المدني</label>
                        <input type="text" id="civil-id" name="civil-id" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="file-number" class="block text-sm font-medium text-gray-700 mb-1">رقم الملف</label>
                        <input type="text" id="file-number" name="file-number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-1">القسم</label>
                        <select id="department" name="department" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر القسم</option>
                            <option value="تقنية المعلومات">تقنية المعلومات</option>
                            <option value="الموارد البشرية">الموارد البشرية</option>
                            <option value="المالية">المالية</option>
                            <option value="التسويق">التسويق</option>
                            <option value="المبيعات">المبيعات</option>
                            <option value="الإدارة">الإدارة</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="position" class="block text-sm font-medium text-gray-700 mb-1">المسمى الوظيفي</label>
                        <input type="text" id="position" name="position" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="hire-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ التوظيف</label>
                        <input type="date" id="hire-date" name="hire-date" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="personal-phone-number" class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف الشخصي</label>
                        <input type="tel" id="personal-phone-number" name="personal-phone-number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="work-phone-number" class="block text-sm font-medium text-gray-700 mb-1">رقم هاتف العمل</label>
                        <input type="tel" id="work-phone-number" name="work-phone-number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="passport-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء جواز السفر</label>
                        <input type="date" id="passport-expiry-date" name="passport-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="residence-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء الإقامة</label>
                        <input type="date" id="residence-expiry-date" name="residence-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="license-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء رخصة العمل</label>
                        <input type="date" id="license-expiry-date" name="license-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="health-card-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء البطاقة الصحية</label>
                        <input type="date" id="health-card-expiry-date" name="health-card-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="flex justify-end gap-3 pt-4">
                    <button type="button" class="close-button bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-6 rounded-lg transition-all">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-primary hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-all">
                        حفظ البيانات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Leave Modal -->
    <div id="add-leave-modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 class="text-2xl font-bold text-gray-800 mb-6">طلب إجازة جديد</h2>

            <form id="leave-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="leave-employee" class="block text-sm font-medium text-gray-700 mb-1">الموظف</label>
                        <select id="leave-employee" name="leave-employee" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر الموظف</option>
                            <option value="طارق عبدالله خالد">طارق عبدالله خالد</option>
                            <option value="نورة أحمد سعد">نورة أحمد سعد</option>
                        </select>
                    </div>
                    <div>
                        <label for="leave-type" class="block text-sm font-medium text-gray-700 mb-1">نوع الإجازة</label>
                        <select id="leave-type" name="leave-type" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر نوع الإجازة</option>
                            <option value="سنوية">إجازة سنوية</option>
                            <option value="مرضية">إجازة مرضية</option>
                            <option value="طارئة">إجازة طارئة</option>
                            <option value="أمومة">إجازة أمومة</option>
                            <option value="حج">إجازة حج</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="leave-start-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية</label>
                        <input type="date" id="leave-start-date" name="leave-start-date" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="leave-end-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ النهاية</label>
                        <input type="date" id="leave-end-date" name="leave-end-date" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div>
                    <label for="leave-reason" class="block text-sm font-medium text-gray-700 mb-1">سبب الإجازة</label>
                    <textarea id="leave-reason" name="leave-reason" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50" placeholder="اكتب سبب طلب الإجازة..."></textarea>
                </div>

                <div class="flex justify-end gap-3 pt-4">
                    <button type="button" class="close-button bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-6 rounded-lg transition-all">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-all">
                        إرسال الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Upload Document Modal -->
    <div id="upload-document-modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 class="text-2xl font-bold text-gray-800 mb-6">رفع وثيقة جديدة</h2>

            <form id="document-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="document-employee" class="block text-sm font-medium text-gray-700 mb-1">الموظف</label>
                        <select id="document-employee" name="document-employee" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر الموظف</option>
                        </select>
                    </div>
                    <div>
                        <label for="document-type" class="block text-sm font-medium text-gray-700 mb-1">نوع الوثيقة</label>
                        <select id="document-type" name="document-type" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر نوع الوثيقة</option>
                            <option value="passport">جواز السفر</option>
                            <option value="residence">الإقامة</option>
                            <option value="license">رخصة العمل</option>
                            <option value="health">البطاقة الصحية</option>
                            <option value="contract">عقد العمل</option>
                            <option value="certificate">شهادة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label for="document-title" class="block text-sm font-medium text-gray-700 mb-1">عنوان الوثيقة</label>
                    <input type="text" id="document-title" name="document-title" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50" placeholder="مثال: جواز السفر - طارق عبدالله">
                </div>

                <div>
                    <label for="document-file" class="block text-sm font-medium text-gray-700 mb-1">الملف</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors">
                        <input type="file" id="document-file" name="document-file" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required class="hidden">
                        <div id="file-drop-area" class="cursor-pointer" onclick="document.getElementById('document-file').click()">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">اضغط لاختيار الملف أو اسحبه هنا</p>
                            <p class="text-sm text-gray-400 mt-1">PDF, JPG, PNG, DOC (حد أقصى 10MB)</p>
                        </div>
                        <div id="file-info" class="hidden mt-3 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-file text-blue-600"></i>
                                    <span id="file-name" class="text-sm font-medium"></span>
                                </div>
                                <button type="button" onclick="clearFile()" class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="mt-2">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="upload-progress" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="document-expiry" class="block text-sm font-medium text-gray-700 mb-1">تاريخ الانتهاء (اختياري)</label>
                        <input type="date" id="document-expiry" name="document-expiry" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="document-category" class="block text-sm font-medium text-gray-700 mb-1">التصنيف</label>
                        <select id="document-category" name="document-category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="personal">وثائق شخصية</option>
                            <option value="work">وثائق العمل</option>
                            <option value="legal">وثائق قانونية</option>
                            <option value="medical">وثائق طبية</option>
                            <option value="educational">وثائق تعليمية</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label for="document-notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                    <textarea id="document-notes" name="document-notes" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50" placeholder="أي ملاحظات إضافية حول الوثيقة..."></textarea>
                </div>

                <div class="flex justify-end gap-3 pt-4">
                    <button type="button" class="close-button bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-6 rounded-lg transition-all">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-lg transition-all">
                        <i class="fas fa-upload mr-2"></i>
                        رفع الوثيقة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer Section -->
    <footer class="bg-primary text-white mt-8 pt-8 pb-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between gap-8">
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">نظام إدارة الموارد البشريّة</h3>
                    <p class="text-sm opacity-80 leading-relaxed">
                        نظام متكامل لإدارة الموارد البشرية للشركات في دولة الكويت، يشمل نظام الحضور، الرواتب، الإجازات، وشؤون ومعلومات الموظفين.
                    </p>
                </div>
                
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">روابط سريعة</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">لوحة التحكم</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الموظفون</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الحضور والانصراف</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الإجازات</a></li>
                    </ul>
                </div>
                
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">الدعم الفني</h3>
                    <ul class="space-y-3">
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-map-marker-alt mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80">مدينة الكويت - برج التحرير</span>
                        </li>
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-phone-alt mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80">+965 22212345</span>
                        </li>
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-envelope mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-sm opacity-80">
                <p>&copy; 2023 نظام إدارة الموارد البشريّّة - جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
    
    <script>
        // Toggle mobile menu
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const nav = document.getElementById('main-nav');
            nav.classList.toggle('hidden');
            if(!nav.classList.contains('hidden')) {
                nav.classList.add('block', 'py-3', 'px-3', 'bg-white/10', 'rounded-lg', 'mt-2');
            }
        });
        
        // Set current date
        const date = new Date();
        const formattedDate = date.toLocaleDateString('ar-KW', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('current-date').textContent = 'اليوم: ' + formattedDate;
        
        // Enhanced tab switching with proper functionality
        document.querySelectorAll('nav ul li a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Get target tab
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all nav items
                document.querySelectorAll('nav ul li a').forEach(el => {
                    el.parentElement.classList.remove('bg-white/20');
                    el.classList.remove('font-bold', 'text-blue-100');
                });

                // Add active state to clicked item
                this.classList.add('font-bold', 'text-blue-100');
                this.parentElement.classList.add('bg-white/20');

                // Show target tab content
                if (targetTab) {
                    showTab(targetTab);
                }

                // Close mobile menu if open
                const nav = document.getElementById('main-nav');
                if (nav.classList.contains('block')) {
                    nav.classList.add('hidden');
                    nav.classList.remove('block', 'py-3', 'px-3', 'bg-white/10', 'rounded-lg', 'mt-2');
                }
            });
        });

        // Active dashboard nav item by default
        document.querySelector('[data-tab="dashboard-section"]').classList.add('font-bold', 'text-blue-100');
        document.querySelector('[data-tab="dashboard-section"]').parentElement.classList.add('bg-white/20');
        
        // Enhanced modal functionality
        document.getElementById('add-employee-btn').addEventListener('click', function() {
            document.getElementById('employee-form').reset();
            document.getElementById('form-title').textContent = 'إضافة موظف جديد';
            document.getElementById('add-employee-modal').style.display = 'flex';
        });

        // Add Leave button functionality
        document.getElementById('add-leave-btn').addEventListener('click', function() {
            document.getElementById('leave-form').reset();
            document.getElementById('add-leave-modal').style.display = 'flex';
        });

        // Close modals
        document.querySelectorAll('.close-button').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
        
        // Function to show message
        function showMessage(message, type = 'success') {
            const messageBox = document.getElementById('message-box');
            messageBox.textContent = message;
            messageBox.className = `p-3 rounded-lg text-white mb-4 text-center animate-fade-in ${type === 'error' ? 'bg-red-500' : 'bg-green-500'}`;
            messageBox.style.display = 'block';
            
            setTimeout(() => {
                messageBox.style.display = 'none';
            }, 5000);
        }

        // Local Storage Database System
        let employees = JSON.parse(localStorage.getItem('hrms_employees') || '[]');
        let leaves = JSON.parse(localStorage.getItem('hrms_leaves') || '[]');
        let attendance = JSON.parse(localStorage.getItem('hrms_attendance') || '[]');
        let salaries = JSON.parse(localStorage.getItem('hrms_salaries') || '[]');

        // Initialize with sample data if empty
        function initializeSampleData() {
            if (employees.length === 0) {
                employees = [
                    {
                        id: 'emp001',
                        first_name: 'طارق',
                        last_name: 'عبدالله خالد',
                        email: '<EMAIL>',
                        civil_id: '293012301230',
                        file_number: 'EMP001',
                        department: 'تقنية المعلومات',
                        position: 'مطور ويب',
                        hire_date: '2022-01-15',
                        personal_phone_number: '+965 50012345',
                        work_phone_number: '+965 22334455',
                        passport_expiry_date: '2025-06-15',
                        residence_expiry_date: '2024-12-31',
                        license_expiry_date: '2024-08-20',
                        health_card_expiry_date: '2024-11-30',
                        status: 'active',
                        created_at: '2022-01-15T08:00:00.000Z',
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp002',
                        first_name: 'نورة',
                        last_name: 'أحمد سعد',
                        email: '<EMAIL>',
                        civil_id: '293082301230',
                        file_number: 'EMP002',
                        department: 'الموارد البشرية',
                        position: 'أخصائي شؤون الموظفين',
                        hire_date: '2021-09-10',
                        personal_phone_number: '+965 50054321',
                        work_phone_number: '+965 22334466',
                        passport_expiry_date: '2026-03-20',
                        residence_expiry_date: '2024-09-15',
                        license_expiry_date: '2024-07-10',
                        health_card_expiry_date: '2024-10-25',
                        status: 'active',
                        created_at: '2021-09-10T08:00:00.000Z',
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp003',
                        first_name: 'فهد',
                        last_name: 'عبدالعزيز محمد',
                        email: '<EMAIL>',
                        civil_id: '293092301230',
                        file_number: 'EMP003',
                        department: 'المالية',
                        position: 'محاسب أول',
                        hire_date: '2020-03-01',
                        personal_phone_number: '+965 50098765',
                        work_phone_number: '+965 22334477',
                        passport_expiry_date: '2025-12-10',
                        residence_expiry_date: '2024-06-30',
                        license_expiry_date: '2024-05-15',
                        health_card_expiry_date: '2024-08-20',
                        status: 'active',
                        created_at: '2020-03-01T08:00:00.000Z',
                        updated_at: new Date().toISOString()
                    }
                ];

                leaves = [
                    {
                        id: 'leave001',
                        employee: 'طارق عبدالله خالد',
                        type: 'سنوية',
                        start_date: '2024-02-01',
                        end_date: '2024-02-05',
                        reason: 'إجازة سنوية للراحة',
                        status: 'approved',
                        created_at: '2024-01-15T10:00:00.000Z'
                    },
                    {
                        id: 'leave002',
                        employee: 'نورة أحمد سعد',
                        type: 'مرضية',
                        start_date: '2024-01-20',
                        end_date: '2024-01-22',
                        reason: 'إجازة مرضية',
                        status: 'pending',
                        created_at: '2024-01-18T09:00:00.000Z'
                    }
                ];

                // Generate sample attendance for today
                const today = new Date().toISOString().split('T')[0];
                attendance = [
                    {
                        id: 'att001',
                        employee_id: 'emp001',
                        employee_name: 'طارق عبدالله خالد',
                        date: today,
                        check_in: '08:45',
                        check_out: '17:12',
                        hours_worked: 8.45,
                        status: 'present'
                    },
                    {
                        id: 'att002',
                        employee_id: 'emp002',
                        employee_name: 'نورة أحمد سعد',
                        date: today,
                        check_in: '08:32',
                        check_out: null,
                        hours_worked: 0,
                        status: 'present'
                    },
                    {
                        id: 'att003',
                        employee_id: 'emp003',
                        employee_name: 'فهد عبدالعزيز محمد',
                        date: today,
                        check_in: '09:15',
                        check_out: '17:30',
                        hours_worked: 8.25,
                        status: 'present'
                    }
                ];

                saveData();
            }
        }

        // Generate unique ID
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // Save data to localStorage
        function saveData() {
            localStorage.setItem('hrms_employees', JSON.stringify(employees));
            localStorage.setItem('hrms_leaves', JSON.stringify(leaves));
            localStorage.setItem('hrms_attendance', JSON.stringify(attendance));
            localStorage.setItem('hrms_salaries', JSON.stringify(salaries));
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', () => {
            try {
                // Initialize sample data if needed
                initializeSampleData();

                // Set user display
                document.getElementById('user-id-display').textContent = 'مرحباً بك في النظام';

                // Load initial data
                loadEmployees();
                loadLeaves();
                loadAttendance();
                updateDashboardStats();
                loadNotifications();
                initializeCharts();
                updateReportStats();

                // Show dashboard by default
                showTab('dashboard-section');

            } catch (error) {
                console.error("Application initialization error:", error);
                showMessage(`حدث خطأ في تهيئة النظام: ${error.message}`, 'error');
            }

            // Add Employee Form Submission
            const employeeForm = document.getElementById('employee-form');
            if (employeeForm) {
                employeeForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    const employeeData = {
                        id: document.getElementById('employee-id-field').value || generateId(),
                        first_name: document.getElementById('first-name').value,
                        last_name: document.getElementById('last-name').value,
                        email: document.getElementById('email').value,
                        file_number: document.getElementById('file-number').value,
                        civil_id: document.getElementById('civil-id').value,
                        department: document.getElementById('department').value,
                        position: document.getElementById('position').value,
                        hire_date: document.getElementById('hire-date').value,
                        residence_expiry_date: document.getElementById('residence-expiry-date').value,
                        passport_expiry_date: document.getElementById('passport-expiry-date').value,
                        personal_phone_number: document.getElementById('personal-phone-number').value,
                        work_phone_number: document.getElementById('work-phone-number').value,
                        license_expiry_date: document.getElementById('license-expiry-date').value,
                        health_card_expiry_date: document.getElementById('health-card-expiry-date').value,
                        status: 'active',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };

                    try {
                        const existingIndex = employees.findIndex(emp => emp.id === employeeData.id);

                        if (existingIndex !== -1) {
                            // Update existing employee
                            employees[existingIndex] = { ...employees[existingIndex], ...employeeData };
                            showMessage('تم تحديث بيانات الموظف بنجاح');
                        } else {
                            // Add new employee
                            employees.push(employeeData);
                            showMessage('تم إضافة الموظف بنجاح');
                        }

                        // Save to localStorage
                        saveData();

                        // Close modal and refresh list
                        document.getElementById('add-employee-modal').style.display = 'none';
                        loadEmployees();
                        updateDashboardStats();
                        updateReportStats();
                        updateCharts();

                    } catch (error) {
                        console.error("Error saving employee:", error);
                        showMessage(`حدث خطأ أثناء حفظ البيانات: ${error.message}`, 'error');
                    }
                });
            }

            // Add Leave Form Submission
            const leaveForm = document.getElementById('leave-form');
            if (leaveForm) {
                leaveForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    const leaveData = {
                        id: generateId(),
                        employee: document.getElementById('leave-employee').value,
                        type: document.getElementById('leave-type').value,
                        start_date: document.getElementById('leave-start-date').value,
                        end_date: document.getElementById('leave-end-date').value,
                        reason: document.getElementById('leave-reason').value,
                        status: 'pending',
                        created_at: new Date().toISOString()
                    };

                    try {
                        leaves.push(leaveData);
                        saveData();

                        showMessage('تم إرسال طلب الإجازة بنجاح');
                        document.getElementById('add-leave-modal').style.display = 'none';
                        loadLeaves();
                        updateDashboardStats();

                    } catch (error) {
                        console.error("Error saving leave:", error);
                        showMessage(`حدث خطأ أثناء حفظ طلب الإجازة: ${error.message}`, 'error');
                    }
                });
            }

        });

        // Function to load employees
        function loadEmployees() {
            const employeeListDiv = document.getElementById('employee-list');
            if (!employeeListDiv) return;

            employeeListDiv.innerHTML = ''; // Clear current list

            if (employees.length === 0) {
                employeeListDiv.innerHTML = `
                    <div class="bg-gray-50 p-8 rounded-xl text-center">
                        <i class="fas fa-users-slash text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">لا يوجد موظفون مسجلون بعد</p>
                        <button onclick="document.getElementById('add-employee-modal').style.display='flex'" class="mt-4 bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all">
                            إضافة أول موظف
                        </button>
                    </div>
                `;
                return;
            }

            employees.forEach(employee => {
                employeeListDiv.appendChild(createEmployeeCard(employee.id, employee));
            });

            // Update attendance select dropdown
            populateAttendanceSelect();
        }

        // Function to load leaves
        function loadLeaves() {
            const leavesListDiv = document.getElementById('leaves-list');
            if (!leavesListDiv) return;

            // Update leaves count in dashboard
            const leavesCount = leaves.filter(leave => leave.status === 'approved').length;
            const pendingCount = leaves.filter(leave => leave.status === 'pending').length;

            // Update leave statistics if elements exist
            const employeesOnLeaveElement = document.getElementById('employees-on-leave-count');
            if (employeesOnLeaveElement) {
                employeesOnLeaveElement.textContent = leavesCount;
            }
        }

        // Function to load attendance
        function loadAttendance() {
            // Update attendance count in dashboard
            const todayAttendance = attendance.filter(att => {
                const today = new Date().toDateString();
                const attDate = new Date(att.date).toDateString();
                return today === attDate;
            });

            const attendanceCountElement = document.getElementById('attendance-count');
            if (attendanceCountElement) {
                attendanceCountElement.textContent = todayAttendance.length;
            }
        }

        // Function to create employee card
        function createEmployeeCard(id, employee) {
            const card = document.createElement('div');
            card.className = 'employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md';
            card.innerHTML = `
                <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                    <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                </div>
                <div class="p-4 sm:p-6 flex-grow">
                    <div class="flex flex-wrap justify-between">
                        <h3 class="text-xl font-bold text-gray-800">${employee.first_name || ''} ${employee.last_name || ''}</h3>
                        <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">${employee.status === 'active' ? 'نشيط' : 'غير نشط'}</span>
                    </div>
                    
                    <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-id-card text-gray-400 w-5"></i>
                            <span class="font-medium">الرقم المدني: ${employee.civil_id || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                            <span>${employee.personal_phone_number || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-building text-gray-400 w-5"></i>
                            <span>الأقسام: ${employee.department || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-briefcase text-gray-400 w-5"></i>
                            <span>المسمى الوظيفي: ${employee.position || 'غير محدد'}</span>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="viewEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-user-circle text-blue-600"></i>
                            عرض الملف الشخصي
                        </button>
                        <button onclick="editEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-edit text-yellow-600"></i>
                            تعديل البيانات
                        </button>
                        <button onclick="deleteEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-trash-alt text-red-600"></i>
                            حذف الموظف
                        </button>
                    </div>
                </div>
            `;
            return card;
        }

        // Function to edit employee
        window.editEmployee = function(id) {
            const employee = employees.find(emp => emp.id === id);

            if (employee) {
                document.getElementById('form-title').textContent = 'تعديل بيانات الموظف';
                document.getElementById('employee-id-field').value = id;

                // Set form values
                document.getElementById('first-name').value = employee.first_name || '';
                document.getElementById('last-name').value = employee.last_name || '';
                document.getElementById('email').value = employee.email || '';
                document.getElementById('file-number').value = employee.file_number || '';
                document.getElementById('civil-id').value = employee.civil_id || '';
                document.getElementById('department').value = employee.department || '';
                document.getElementById('position').value = employee.position || '';
                document.getElementById('hire-date').value = employee.hire_date || '';
                document.getElementById('residence-expiry-date').value = employee.residence_expiry_date || '';
                document.getElementById('passport-expiry-date').value = employee.passport_expiry_date || '';
                document.getElementById('license-expiry-date').value = employee.license_expiry_date || '';
                document.getElementById('health-card-expiry-date').value = employee.health_card_expiry_date || '';
                document.getElementById('personal-phone-number').value = employee.personal_phone_number || '';
                document.getElementById('work-phone-number').value = employee.work_phone_number || '';

                document.getElementById('add-employee-modal').style.display = 'flex';
            } else {
                showMessage('لم يتم العثور على بيانات الموظف', 'error');
            }
        };

        // Function to delete employee
        window.deleteEmployee = function(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                try {
                    employees = employees.filter(emp => emp.id !== id);
                    saveData();
                    showMessage('تم حذف الموظف بنجاح');
                    loadEmployees();
                    updateDashboardStats();
                } catch (error) {
                    showMessage('حدث خطأ أثناء حذف الموظف', 'error');
                }
            }
        };

        // Function to view employee details
        window.viewEmployee = function(id) {
            const employee = employees.find(emp => emp.id === id);
            if (employee) {
                alert(`تفاصيل الموظف:\n\nالاسم: ${employee.first_name} ${employee.last_name}\nالقسم: ${employee.department}\nالمنصب: ${employee.position}\nالرقم المدني: ${employee.civil_id}\nالهاتف: ${employee.personal_phone_number}`);
            }
        };

        // Function to update dashboard stats
        function updateDashboardStats() {
            const totalEmployeesElement = document.getElementById('total-employees-count');
            if (totalEmployeesElement) {
                totalEmployeesElement.textContent = employees.length;
            }

            const leavesCountElement = document.getElementById('leaves-count');
            if (leavesCountElement) {
                leavesCountElement.textContent = leaves.filter(leave => leave.status === 'approved').length;
            }

            const attendanceCountElement = document.getElementById('attendance-count');
            if (attendanceCountElement) {
                const today = new Date().toDateString();
                const todayAttendance = attendance.filter(att => {
                    const attDate = new Date(att.date).toDateString();
                    return today === attDate;
                });
                attendanceCountElement.textContent = todayAttendance.length;
            }
        }

        // Function to switch tabs
        function showTab(tabId) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }
        }

        // Search functionality
        function searchEmployees(query) {
            if (!query.trim()) {
                loadEmployees();
                return;
            }

            const filteredEmployees = employees.filter(employee => {
                const fullName = `${employee.first_name} ${employee.last_name}`.toLowerCase();
                const civilId = employee.civil_id || '';
                const department = employee.department || '';
                const position = employee.position || '';

                return fullName.includes(query.toLowerCase()) ||
                       civilId.includes(query) ||
                       department.toLowerCase().includes(query.toLowerCase()) ||
                       position.toLowerCase().includes(query.toLowerCase());
            });

            displayFilteredEmployees(filteredEmployees);
        }

        // Display filtered employees
        function displayFilteredEmployees(filteredEmployees) {
            const employeeListDiv = document.getElementById('employee-list');
            if (!employeeListDiv) return;

            employeeListDiv.innerHTML = '';

            if (filteredEmployees.length === 0) {
                employeeListDiv.innerHTML = `
                    <div class="bg-gray-50 p-8 rounded-xl text-center">
                        <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">لم يتم العثور على نتائج مطابقة</p>
                    </div>
                `;
                return;
            }

            filteredEmployees.forEach(employee => {
                employeeListDiv.appendChild(createEmployeeCard(employee.id, employee));
            });
        }

        // Export employees to CSV
        function exportEmployeesToCSV() {
            if (employees.length === 0) {
                showMessage('لا توجد بيانات للتصدير', 'error');
                return;
            }

            const headers = ['الاسم الأول', 'اسم العائلة', 'الرقم المدني', 'القسم', 'المنصب', 'الهاتف', 'البريد الإلكتروني', 'تاريخ التوظيف'];
            const csvContent = [
                headers.join(','),
                ...employees.map(emp => [
                    emp.first_name || '',
                    emp.last_name || '',
                    emp.civil_id || '',
                    emp.department || '',
                    emp.position || '',
                    emp.personal_phone_number || '',
                    emp.email || '',
                    emp.hire_date || ''
                ].join(','))
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `employees_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('تم تصدير البيانات بنجاح');
        }

        // Print employees list
        function printEmployeesList() {
            if (employees.length === 0) {
                showMessage('لا توجد بيانات للطباعة', 'error');
                return;
            }

            const printWindow = window.open('', '_blank');
            const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>قائمة الموظفين</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        h1 { text-align: center; color: #2563eb; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .date { text-align: left; margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>نظام إدارة الموارد البشرية - الكويت</h1>
                        <h2>قائمة الموظفين</h2>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الرقم المدني</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>الهاتف</th>
                                <th>تاريخ التوظيف</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${employees.map(emp => `
                                <tr>
                                    <td>${emp.first_name} ${emp.last_name}</td>
                                    <td>${emp.civil_id || ''}</td>
                                    <td>${emp.department || ''}</td>
                                    <td>${emp.position || ''}</td>
                                    <td>${emp.personal_phone_number || ''}</td>
                                    <td>${emp.hire_date || ''}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    <div class="date">
                        تاريخ الطباعة: ${new Date().toLocaleDateString('ar-KW')}
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        // Add event listeners for search and actions
        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality
            const searchInput = document.getElementById('employee-search');
            const searchBtn = document.getElementById('search-btn');

            if (searchInput && searchBtn) {
                searchInput.addEventListener('input', function() {
                    searchEmployees(this.value);
                });

                searchBtn.addEventListener('click', function() {
                    searchEmployees(searchInput.value);
                });

                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchEmployees(this.value);
                    }
                });
            }

            // Export functionality
            const exportBtn = document.getElementById('export-employees-btn');
            if (exportBtn) {
                exportBtn.addEventListener('click', exportEmployeesToCSV);
            }

            // Print functionality
            const printBtn = document.getElementById('print-employees-btn');
            if (printBtn) {
                printBtn.addEventListener('click', printEmployeesList);
            }

            // Clock in/out functionality
            const clockBtn = document.getElementById('clock-in-out-btn');
            if (clockBtn) {
                clockBtn.addEventListener('click', handleClockInOut);
            }

            // Document upload functionality
            const uploadBtn = document.getElementById('upload-document-btn');
            if (uploadBtn) {
                uploadBtn.addEventListener('click', function() {
                    populateDocumentEmployeeSelect();
                    document.getElementById('upload-document-modal').style.display = 'flex';
                });
            }

            // File input change handler
            const fileInput = document.getElementById('document-file');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }
        });

        // Handle clock in/out
        function handleClockInOut() {
            const selectedEmployee = document.getElementById('attendance-employee-select').value;
            if (!selectedEmployee || selectedEmployee === '-- اختر الموظف --') {
                showMessage('يرجى اختيار الموظف أولاً', 'error');
                return;
            }

            const today = new Date().toISOString().split('T')[0];
            const currentTime = new Date().toLocaleTimeString('en-GB', { hour12: false }).substring(0, 5);

            // Find existing attendance record for today
            const existingAttendance = attendance.find(att =>
                att.employee_name === selectedEmployee && att.date === today
            );

            if (existingAttendance) {
                if (!existingAttendance.check_out) {
                    // Clock out
                    existingAttendance.check_out = currentTime;
                    const checkIn = new Date(`2000-01-01 ${existingAttendance.check_in}`);
                    const checkOut = new Date(`2000-01-01 ${currentTime}`);
                    const hoursWorked = (checkOut - checkIn) / (1000 * 60 * 60);
                    existingAttendance.hours_worked = Math.round(hoursWorked * 100) / 100;

                    showMessage(`تم تسجيل خروج ${selectedEmployee} في ${currentTime}`);
                } else {
                    showMessage('تم تسجيل الدخول والخروج لهذا الموظف اليوم', 'error');
                    return;
                }
            } else {
                // Clock in
                const newAttendance = {
                    id: generateId(),
                    employee_id: employees.find(emp => `${emp.first_name} ${emp.last_name}` === selectedEmployee)?.id || '',
                    employee_name: selectedEmployee,
                    date: today,
                    check_in: currentTime,
                    check_out: null,
                    hours_worked: 0,
                    status: 'present'
                };

                attendance.push(newAttendance);
                showMessage(`تم تسجيل دخول ${selectedEmployee} في ${currentTime}`);
            }

            saveData();
            loadAttendance();
            updateDashboardStats();
        }

        // Populate attendance employee select
        function populateAttendanceSelect() {
            const select = document.getElementById('attendance-employee-select');
            if (select && employees.length > 0) {
                // Clear existing options except the first one
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }

                employees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = `${employee.first_name} ${employee.last_name}`;
                    option.textContent = `${employee.first_name} ${employee.last_name}`;
                    select.appendChild(option);
                });
            }
        }

        // Advanced Notifications System
        function loadNotifications() {
            const notificationsContainer = document.getElementById('expiration-notifications');
            if (!notificationsContainer) return;

            const notifications = generateNotifications();
            notificationsContainer.innerHTML = '';

            if (notifications.length === 0) {
                notificationsContainer.innerHTML = `
                    <div class="p-4 rounded-xl bg-green-50 border border-green-100 text-center">
                        <div class="w-12 h-12 rounded-lg bg-green-500/10 text-green-600 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-check-circle text-2xl"></i>
                        </div>
                        <p class="font-medium text-green-700">لا توجد تنبيهات عاجلة</p>
                        <p class="text-sm text-gray-600 mt-1">جميع الوثائق والمواعيد محدثة</p>
                    </div>
                `;
                return;
            }

            notifications.forEach(notification => {
                const notificationElement = createNotificationElement(notification);
                notificationsContainer.appendChild(notificationElement);
            });
        }

        // Generate notifications based on employee data
        function generateNotifications() {
            const notifications = [];
            const today = new Date();

            employees.forEach(employee => {
                // Check passport expiry
                if (employee.passport_expiry_date) {
                    const expiryDate = new Date(employee.passport_expiry_date);
                    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

                    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                        notifications.push({
                            type: 'passport',
                            priority: daysUntilExpiry <= 7 ? 'high' : 'medium',
                            employee: `${employee.first_name} ${employee.last_name}`,
                            message: `تاريخ انتهاء جواز السفر`,
                            details: `تنتهي في: ${employee.passport_expiry_date} (بعد ${daysUntilExpiry} يوم)`,
                            icon: 'fas fa-passport',
                            daysLeft: daysUntilExpiry
                        });
                    } else if (daysUntilExpiry <= 0) {
                        notifications.push({
                            type: 'passport',
                            priority: 'critical',
                            employee: `${employee.first_name} ${employee.last_name}`,
                            message: `جواز السفر منتهي الصلاحية`,
                            details: `انتهت الصلاحية في: ${employee.passport_expiry_date}`,
                            icon: 'fas fa-passport',
                            daysLeft: daysUntilExpiry
                        });
                    }
                }

                // Check residence expiry
                if (employee.residence_expiry_date) {
                    const expiryDate = new Date(employee.residence_expiry_date);
                    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

                    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                        notifications.push({
                            type: 'residence',
                            priority: daysUntilExpiry <= 7 ? 'high' : 'medium',
                            employee: `${employee.first_name} ${employee.last_name}`,
                            message: `تاريخ انتهاء الإقامة`,
                            details: `تنتهي في: ${employee.residence_expiry_date} (بعد ${daysUntilExpiry} يوم)`,
                            icon: 'fas fa-id-card',
                            daysLeft: daysUntilExpiry
                        });
                    }
                }

                // Check license expiry
                if (employee.license_expiry_date) {
                    const expiryDate = new Date(employee.license_expiry_date);
                    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

                    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                        notifications.push({
                            type: 'license',
                            priority: daysUntilExpiry <= 7 ? 'high' : 'medium',
                            employee: `${employee.first_name} ${employee.last_name}`,
                            message: `تاريخ انتهاء رخصة العمل`,
                            details: `تنتهي في: ${employee.license_expiry_date} (بعد ${daysUntilExpiry} يوم)`,
                            icon: 'fas fa-briefcase',
                            daysLeft: daysUntilExpiry
                        });
                    }
                }

                // Check health card expiry
                if (employee.health_card_expiry_date) {
                    const expiryDate = new Date(employee.health_card_expiry_date);
                    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

                    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                        notifications.push({
                            type: 'health_card',
                            priority: daysUntilExpiry <= 7 ? 'high' : 'medium',
                            employee: `${employee.first_name} ${employee.last_name}`,
                            message: `تاريخ انتهاء البطاقة الصحية`,
                            details: `تنتهي في: ${employee.health_card_expiry_date} (بعد ${daysUntilExpiry} يوم)`,
                            icon: 'fas fa-heartbeat',
                            daysLeft: daysUntilExpiry
                        });
                    }
                }

                // Check work anniversary
                if (employee.hire_date) {
                    const hireDate = new Date(employee.hire_date);
                    const thisYearAnniversary = new Date(today.getFullYear(), hireDate.getMonth(), hireDate.getDate());
                    const daysUntilAnniversary = Math.ceil((thisYearAnniversary - today) / (1000 * 60 * 60 * 24));

                    if (daysUntilAnniversary <= 30 && daysUntilAnniversary >= 0) {
                        const yearsOfService = today.getFullYear() - hireDate.getFullYear();
                        notifications.push({
                            type: 'anniversary',
                            priority: 'low',
                            employee: `${employee.first_name} ${employee.last_name}`,
                            message: `الذكرى السنوية للتوظيف`,
                            details: `${yearsOfService} سنوات من الخدمة - بعد ${daysUntilAnniversary} يوم`,
                            icon: 'fas fa-calendar-check',
                            daysLeft: daysUntilAnniversary
                        });
                    }
                }
            });

            // Sort notifications by priority and days left
            return notifications.sort((a, b) => {
                const priorityOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 };
                if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                    return priorityOrder[a.priority] - priorityOrder[b.priority];
                }
                return a.daysLeft - b.daysLeft;
            });
        }

        // Create notification element
        function createNotificationElement(notification) {
            const div = document.createElement('div');

            let bgColor, borderColor, textColor, iconColor;
            switch (notification.priority) {
                case 'critical':
                    bgColor = 'bg-red-50';
                    borderColor = 'border-red-200';
                    textColor = 'text-red-700';
                    iconColor = 'text-red-600';
                    break;
                case 'high':
                    bgColor = 'bg-orange-50';
                    borderColor = 'border-orange-200';
                    textColor = 'text-orange-700';
                    iconColor = 'text-orange-600';
                    break;
                case 'medium':
                    bgColor = 'bg-yellow-50';
                    borderColor = 'border-yellow-200';
                    textColor = 'text-yellow-700';
                    iconColor = 'text-yellow-600';
                    break;
                case 'low':
                    bgColor = 'bg-blue-50';
                    borderColor = 'border-blue-200';
                    textColor = 'text-blue-700';
                    iconColor = 'text-blue-600';
                    break;
            }

            div.className = `p-4 rounded-xl ${bgColor} border ${borderColor} animate-fade-in`;
            div.innerHTML = `
                <div class="flex gap-3 items-start">
                    <div class="w-10 h-10 rounded-lg bg-white/50 ${iconColor} flex items-center justify-center flex-shrink-0">
                        <i class="${notification.icon} text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="font-medium ${textColor}">${notification.message}</p>
                                <p class="text-sm text-gray-600 mt-1">${notification.employee} | ${notification.details}</p>
                            </div>
                            <div class="flex gap-2 ml-3">
                                <button onclick="markNotificationAsRead('${notification.type}', '${notification.employee}')"
                                        class="text-gray-400 hover:text-gray-600 transition-colors">
                                    <i class="fas fa-times text-sm"></i>
                                </button>
                                <button onclick="contactEmployee('${notification.employee}')"
                                        class="text-gray-400 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-envelope text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return div;
        }

        // Mark notification as read (hide it)
        window.markNotificationAsRead = function(type, employee) {
            // In a real app, this would update the database
            showMessage(`تم تجاهل التنبيه لـ ${employee}`, 'success');
            loadNotifications(); // Reload notifications
        };

        // Contact employee function
        window.contactEmployee = function(employeeName) {
            const employee = employees.find(emp => `${emp.first_name} ${emp.last_name}` === employeeName);
            if (employee && employee.personal_phone_number) {
                if (confirm(`هل تريد الاتصال بـ ${employeeName} على الرقم ${employee.personal_phone_number}؟`)) {
                    window.open(`tel:${employee.personal_phone_number}`);
                }
            } else {
                showMessage('لا يوجد رقم هاتف مسجل لهذا الموظف', 'error');
            }
        };

        // Charts initialization and management
        let departmentChart, attendanceChart;

        function initializeCharts() {
            createDepartmentChart();
            createAttendanceChart();
            createPerformanceChart();
        }

        function createDepartmentChart() {
            const ctx = document.getElementById('departmentChart');
            if (!ctx) return;

            // Calculate department distribution
            const departmentCounts = {};
            employees.forEach(employee => {
                const dept = employee.department || 'غير محدد';
                departmentCounts[dept] = (departmentCounts[dept] || 0) + 1;
            });

            const labels = Object.keys(departmentCounts);
            const data = Object.values(departmentCounts);
            const colors = [
                '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
                '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
            ];

            if (departmentChart) {
                departmentChart.destroy();
            }

            departmentChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors.slice(0, labels.length),
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    family: 'Inter'
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return `${context.label}: ${context.parsed} موظف (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function createAttendanceChart() {
            const ctx = document.getElementById('attendanceChart');
            if (!ctx) return;

            // Generate sample weekly attendance data
            const days = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
            const attendanceData = [85, 92, 88, 90, 87, 89, 45]; // Sample data
            const totalEmployees = employees.length;
            const attendancePercentages = attendanceData.map(count =>
                totalEmployees > 0 ? ((count / totalEmployees) * 100).toFixed(1) : 0
            );

            if (attendanceChart) {
                attendanceChart.destroy();
            }

            attendanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: days,
                    datasets: [{
                        label: 'نسبة الحضور (%)',
                        data: attendancePercentages,
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#10B981',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `نسبة الحضور: ${context.parsed.y}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function createPerformanceChart() {
            const ctx = document.getElementById('performanceChart');
            if (!ctx) return;

            // Sample performance data over months
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
            const performanceData = [3.8, 4.1, 4.0, 4.3, 4.2, 4.5]; // Sample ratings

            if (window.performanceChart) {
                window.performanceChart.destroy();
            }

            window.performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'متوسط التقييم',
                        data: performanceData,
                        borderColor: '#EC4899',
                        backgroundColor: 'rgba(236, 72, 153, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#EC4899',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `متوسط التقييم: ${context.parsed.y}/5`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5,
                            ticks: {
                                callback: function(value) {
                                    return value + '/5';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Update charts when data changes
        function updateCharts() {
            if (departmentChart) {
                createDepartmentChart();
            }
            if (attendanceChart) {
                createAttendanceChart();
            }
            if (window.performanceChart) {
                createPerformanceChart();
            }
        }

        // Document Management Functions
        let documents = JSON.parse(localStorage.getItem('hrms_documents') || '[]');

        function populateDocumentEmployeeSelect() {
            const select = document.getElementById('document-employee');
            if (select && employees.length > 0) {
                // Clear existing options except the first one
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }

                employees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.id;
                    option.textContent = `${employee.first_name} ${employee.last_name}`;
                    select.appendChild(option);
                });
            }
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                const fileInfo = document.getElementById('file-info');
                const fileName = document.getElementById('file-name');
                const dropArea = document.getElementById('file-drop-area');

                // Validate file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    showMessage('حجم الملف كبير جداً. الحد الأقصى 10MB', 'error');
                    event.target.value = '';
                    return;
                }

                // Validate file type
                const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                if (!allowedTypes.includes(file.type)) {
                    showMessage('نوع الملف غير مدعوم. يرجى اختيار PDF, JPG, PNG, أو DOC', 'error');
                    event.target.value = '';
                    return;
                }

                fileName.textContent = file.name;
                dropArea.style.display = 'none';
                fileInfo.classList.remove('hidden');

                // Simulate upload progress
                simulateUploadProgress();
            }
        }

        function simulateUploadProgress() {
            const progressBar = document.getElementById('upload-progress');
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                }
                progressBar.style.width = progress + '%';
            }, 200);
        }

        function clearFile() {
            const fileInput = document.getElementById('document-file');
            const fileInfo = document.getElementById('file-info');
            const dropArea = document.getElementById('file-drop-area');
            const progressBar = document.getElementById('upload-progress');

            fileInput.value = '';
            fileInfo.classList.add('hidden');
            dropArea.style.display = 'block';
            progressBar.style.width = '0%';
        }

        // Document form submission
        document.addEventListener('DOMContentLoaded', function() {
            const documentForm = document.getElementById('document-form');
            if (documentForm) {
                documentForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const fileInput = document.getElementById('document-file');
                    if (!fileInput.files[0]) {
                        showMessage('يرجى اختيار ملف للرفع', 'error');
                        return;
                    }

                    const employeeId = document.getElementById('document-employee').value;
                    const employee = employees.find(emp => emp.id === employeeId);

                    const documentData = {
                        id: generateId(),
                        employee_id: employeeId,
                        employee_name: employee ? `${employee.first_name} ${employee.last_name}` : '',
                        title: document.getElementById('document-title').value,
                        type: document.getElementById('document-type').value,
                        category: document.getElementById('document-category').value,
                        file_name: fileInput.files[0].name,
                        file_size: fileInput.files[0].size,
                        file_type: fileInput.files[0].type,
                        expiry_date: document.getElementById('document-expiry').value,
                        notes: document.getElementById('document-notes').value,
                        upload_date: new Date().toISOString(),
                        status: 'active'
                    };

                    // In a real application, you would upload the file to a server
                    // For this demo, we'll just store the metadata
                    documents.push(documentData);
                    localStorage.setItem('hrms_documents', JSON.stringify(documents));

                    showMessage('تم رفع الوثيقة بنجاح');
                    document.getElementById('upload-document-modal').style.display = 'none';
                    documentForm.reset();
                    clearFile();

                    // Refresh documents view if we're on that tab
                    const documentsSection = document.getElementById('documents-section');
                    if (documentsSection && documentsSection.classList.contains('active')) {
                        loadDocuments();
                    }
                });
            }
        });

        function loadDocuments() {
            // This function would load and display documents
            // For now, we'll just update the counts
            updateDocumentCounts();
        }

        function updateDocumentCounts() {
            const passportCount = documents.filter(doc => doc.type === 'passport').length;
            const residenceCount = documents.filter(doc => doc.type === 'residence').length;
            const licenseCount = documents.filter(doc => doc.type === 'license').length;
            const otherCount = documents.filter(doc => !['passport', 'residence', 'license'].includes(doc.type)).length;

            const passportElement = document.getElementById('passport-count');
            const residenceElement = document.getElementById('residence-count');
            const licenseElement = document.getElementById('license-count');
            const otherElement = document.getElementById('other-count');

            if (passportElement) passportElement.textContent = `${passportCount} وثيقة`;
            if (residenceElement) residenceElement.textContent = `${residenceCount} وثيقة`;
            if (licenseElement) licenseElement.textContent = `${licenseCount} وثيقة`;
            if (otherElement) otherElement.textContent = `${otherCount} وثيقة`;
        }

        function filterDocuments(type) {
            const filter = document.getElementById('document-filter');
            if (filter) {
                filter.value = type === 'other' ? 'other' : type;
                // In a real app, this would filter the displayed documents
                showMessage(`تم تصفية الوثائق: ${getDocumentTypeLabel(type)}`);
            }
        }

        function getDocumentTypeLabel(type) {
            const labels = {
                'passport': 'جوازات السفر',
                'residence': 'الإقامات',
                'license': 'رخص العمل',
                'health': 'البطاقات الصحية',
                'other': 'وثائق أخرى'
            };
            return labels[type] || type;
        }

        // Reports Generation Functions
        function generateReport(type) {
            showMessage(`جاري إنشاء تقرير ${getReportTypeLabel(type)}...`);

            setTimeout(() => {
                switch(type) {
                    case 'employees':
                        generateEmployeesReport();
                        break;
                    case 'attendance':
                        generateAttendanceReport();
                        break;
                    case 'leaves':
                        generateLeavesReport();
                        break;
                    case 'performance':
                        generatePerformanceReport();
                        break;
                    default:
                        showMessage('نوع التقرير غير مدعوم', 'error');
                }
            }, 1000);
        }

        function getReportTypeLabel(type) {
            const labels = {
                'employees': 'الموظفين',
                'attendance': 'الحضور',
                'leaves': 'الإجازات',
                'performance': 'الأداء'
            };
            return labels[type] || type;
        }

        function generateEmployeesReport() {
            const reportData = {
                title: 'تقرير الموظفين الشامل',
                date: new Date().toLocaleDateString('ar-KW'),
                totalEmployees: employees.length,
                activeEmployees: employees.filter(emp => emp.status === 'active').length,
                departments: [...new Set(employees.map(emp => emp.department))].length,
                employees: employees
            };

            downloadReport('employees', reportData);
            showMessage('تم إنشاء تقرير الموظفين بنجاح');
        }

        function generateAttendanceReport() {
            const today = new Date().toISOString().split('T')[0];
            const todayAttendance = attendance.filter(att => att.date === today);

            const reportData = {
                title: 'تقرير الحضور اليومي',
                date: new Date().toLocaleDateString('ar-KW'),
                totalEmployees: employees.length,
                presentToday: todayAttendance.length,
                attendanceRate: employees.length > 0 ? ((todayAttendance.length / employees.length) * 100).toFixed(1) : 0,
                attendance: todayAttendance
            };

            downloadReport('attendance', reportData);
            showMessage('تم إنشاء تقرير الحضور بنجاح');
        }

        function generateLeavesReport() {
            const reportData = {
                title: 'تقرير الإجازات',
                date: new Date().toLocaleDateString('ar-KW'),
                totalLeaves: leaves.length,
                approvedLeaves: leaves.filter(leave => leave.status === 'approved').length,
                pendingLeaves: leaves.filter(leave => leave.status === 'pending').length,
                rejectedLeaves: leaves.filter(leave => leave.status === 'rejected').length,
                leaves: leaves
            };

            downloadReport('leaves', reportData);
            showMessage('تم إنشاء تقرير الإجازات بنجاح');
        }

        function generatePerformanceReport() {
            // Sample performance data
            const avgRating = 4.2;
            const reportData = {
                title: 'تقرير الأداء',
                date: new Date().toLocaleDateString('ar-KW'),
                averageRating: avgRating,
                totalEvaluations: employees.length,
                excellentPerformers: Math.floor(employees.length * 0.3),
                goodPerformers: Math.floor(employees.length * 0.5),
                needsImprovement: Math.floor(employees.length * 0.2)
            };

            downloadReport('performance', reportData);
            showMessage('تم إنشاء تقرير الأداء بنجاح');
        }

        function downloadReport(type, data) {
            const reportContent = generateReportHTML(type, data);
            const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${type}_report_${new Date().toISOString().split('T')[0]}.html`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateReportHTML(type, data) {
            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>${data.title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2563eb; padding-bottom: 20px; }
                        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
                        .stat-card { background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; }
                        .stat-number { font-size: 2em; font-weight: bold; color: #2563eb; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
                        th { background-color: #2563eb; color: white; }
                        .footer { margin-top: 40px; text-align: center; color: #666; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${data.title}</h1>
                        <p>تاريخ التقرير: ${data.date}</p>
                        <p>نظام إدارة الموارد البشرية - الكويت</p>
                    </div>
                    ${generateReportBody(type, data)}
                    <div class="footer">
                        <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة الموارد البشرية</p>
                        <p>&copy; 2024 جميع الحقوق محفوظة</p>
                    </div>
                </body>
                </html>
            `;
        }

        function generateReportBody(type, data) {
            switch(type) {
                case 'employees':
                    return `
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-number">${data.totalEmployees}</div>
                                <div>إجمالي الموظفين</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${data.activeEmployees}</div>
                                <div>الموظفين النشطين</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${data.departments}</div>
                                <div>عدد الأقسام</div>
                            </div>
                        </div>
                        <table>
                            <thead>
                                <tr><th>الاسم</th><th>القسم</th><th>المنصب</th><th>تاريخ التوظيف</th><th>الحالة</th></tr>
                            </thead>
                            <tbody>
                                ${data.employees.map(emp => `
                                    <tr>
                                        <td>${emp.first_name} ${emp.last_name}</td>
                                        <td>${emp.department || ''}</td>
                                        <td>${emp.position || ''}</td>
                                        <td>${emp.hire_date || ''}</td>
                                        <td>${emp.status === 'active' ? 'نشط' : 'غير نشط'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                case 'attendance':
                    return `
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-number">${data.presentToday}</div>
                                <div>حاضر اليوم</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${data.attendanceRate}%</div>
                                <div>نسبة الحضور</div>
                            </div>
                        </div>
                    `;
                default:
                    return '<p>بيانات التقرير غير متوفرة</p>';
            }
        }

        // Update dashboard stats
        function updateReportStats() {
            const totalEmployeesElement = document.getElementById('total-employees-stat');
            const presentTodayElement = document.getElementById('present-today-stat');
            const onLeaveElement = document.getElementById('on-leave-stat');
            const avgPerformanceElement = document.getElementById('avg-performance-stat');

            if (totalEmployeesElement) totalEmployeesElement.textContent = employees.length;
            if (presentTodayElement) {
                const today = new Date().toISOString().split('T')[0];
                const todayAttendance = attendance.filter(att => att.date === today);
                presentTodayElement.textContent = todayAttendance.length;
            }
            if (onLeaveElement) {
                const approvedLeaves = leaves.filter(leave => leave.status === 'approved');
                onLeaveElement.textContent = approvedLeaves.length;
            }
            if (avgPerformanceElement) avgPerformanceElement.textContent = '4.2';
        }
    </script>
</body>
</html>