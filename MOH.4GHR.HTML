<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية - الكويت</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#2563eb',
              secondary: '#3b82f6',
              accent: '#5b21b6',
              text: {
                light: '#f0f9ff',
                dark: '#1e293b'
              }
            },
            boxShadow: {
              card: '0 4px 20px rgba(0, 0, 0, 0.08)',
              button: '0 4px 12px rgba(37, 99, 235, 0.3)'
            }
          }
        }
      }
    </script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
        }
        
        /* Text animations */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(224, 231, 255, 0.5);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(96, 165, 250, 0.7);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.9);
        }
        
        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: #ffffff;
            margin: auto;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 600px;
            position: relative;
        }
        
        .close-button {
            color: #aaa;
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Dashboard cards */
        .dashboard-card {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        /* Print styling */
        @media print {
            body * {
                visibility: hidden;
            }
            .print-section, .print-section * {
                visibility: visible;
            }
            .print-section {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
        }
        
        /* Sidebar menu for mobile */
        @media (max-width: 768px) {
            #main-nav {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.5s ease;
            }
            
            #main-nav.active {
                max-height: 300px;
            }
        }
        
        /* Fade-in animations */
        .fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
        
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        .smooth-shadow {
            box-shadow: 0 10px 25px rgba(0,0,0,0.06);
        }
    </style>
    <!-- Firebase and HRMS functionality remains the same -->
</head>
<body class="min-h-screen flex flex-col">
    <!-- Header Section -->
    <header class="bg-gradient-to-r from-primary to-accent text-white p-4 sm:p-5 shadow-md rounded-b-xl">
        <div class="container mx-auto flex flex-col sm:flex-row justify-between items-stretch sm:items-center">
            <div class="flex justify-between items-center w-full sm:w-auto">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold flex items-center gap-2">
                        <i class="fas fa-user-friends"></i>
                        نظام إدارة الموارد البشرية
                    </h1>
                    <p class="text-xs sm:text-sm opacity-80 mt-1 flex items-center gap-1">
                        <i class="fas fa-map-marker-alt"></i> الكويت - أخصائي في إدارة الموارد البشرية
                    </p>
                </div>
                <button id="menu-toggle" class="sm:hidden block text-2xl">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav id="main-nav" class="mt-3 sm:mt-0 sm:block">
                <ul class="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 md:gap-5 text-sm sm:text-base">
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-dashboard" data-tab="dashboard-section" class="flex items-center gap-2">
                            <i class="fas fa-home w-5 text-center"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-employees" data-tab="employee-section" class="flex items-center gap-2">
                            <i class="fas fa-users w-5 text-center"></i>
                            الموظفون
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-attendance" data-tab="attendance-section" class="flex items-center gap-2">
                            <i class="fas fa-clock w-5 text-center"></i>
                            الحضور
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-leaves" data-tab="leaves-section" class="flex items-center gap-2">
                            <i class="fas fa-calendar-alt w-5 text-center"></i>
                            الإجازات
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-salaries" data-tab="salaries-section" class="flex items-center gap-2">
                            <i class="fas fa-wallet w-5 text-center"></i>
                            الرواتب
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="container mx-auto p-4 sm:p-6 flex-grow">
        <div id="message-box" class="hidden"></div>
        
        <!-- User info and quick stats bar -->
        <div class="flex flex-wrap items-center justify-between gap-4 mb-6 bg-white p-4 rounded-xl shadow-sm">
            <div class="flex-1">
                <div class="flex items-center gap-3">
                    <div class="bg-gradient-to-r from-primary to-accent p-2 rounded-full">
                        <i class="fas fa-user-circle text-white text-xl"></i>
                    </div>
                    <div>
                        <p id="user-id-display" class="text-sm text-gray-600"></p>
                        <p class="font-bold">مرحبا بك في نظام إدارة الموارد البشرية</p>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap gap-2">
                <div class="flex items-center gap-2 bg-blue-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-users text-blue-600"></i>
                    <span class="font-bold text-blue-700">الموظفين: <span id="total-employees-count">0</span></span>
                </div>
                <div class="flex items-center gap-2 bg-yellow-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-clock text-yellow-600"></i>
                    <span class="font-bold text-yellow-700">الحضور: <span id="attendance-count">0</span></span>
                </div>
                <div class="flex items-center gap-2 bg-green-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-calendar-alt text-green-600"></i>
                    <span class="font-bold text-green-700">الإجازات: <span id="leaves-count">0</span></span>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 active fade-in">
            <div class="flex justify-between items-center mb-6 border-b-2 pb-4 border-primary">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">لوحة التحكم</h2>
                <div class="text-sm text-gray-500 hidden sm:block">
                    <i class="far fa-calendar-alt ml-2"></i>
                    <span id="current-date">اليوم: 20 ديسمبر 2023</span>
                </div>
            </div>
            
            <!-- Dashboard Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
                <!-- Card: Total Employees -->
                <div class="dashboard-card bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-users text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold">254</p>
                    <p class="text-lg mt-2">إجمالي الموظفين</p>
                    <p class="flex items-center gap-1 mt-2 text-blue-100">
                        <i class="fas fa-arrow-up text-xs"></i>
                        +12 هذا الشهر
                    </p>
                </div>
                
                <!-- Card: Active Employees -->
                <div class="dashboard-card bg-gradient-to-br from-green-500 to-green-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-user-check text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold">224</p>
                    <p class="text-lg mt-2">الموظفين النشطين</p>
                    <p class="flex items-center gap-1 mt-2 text-green-100">
                        94% حضور
                    </p>
                </div>
                
                <!-- Card: Employees on Leave -->
                <div class="dashboard-card bg-gradient-to-br from-yellow-500 to-yellow-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-calendar-alt text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold" id="employees-on-leave-count">22</p>
                    <p class="text-lg mt-2">الموظفون في إجازة</p>
                    <p class="flex items-center gap-1 mt-2 text-yellow-100">
                        +5 هذا الأسبوع
                    </p>
                </div>
                
                <!-- Card: Upcoming Hires -->
                <div class="dashboard-card bg-gradient-to-br from-violet-500 to-violet-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-user-plus text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold" id="upcoming-hires-count">8</p>
                    <p class="text-lg mt-2">توظيفات قادمة</p>
                    <p class="flex items-center gap-1 mt-2 text-violet-100">
                        خلال 3 أشهر
                    </p>
                </div>
            </div>
            
            <!-- Notifications Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div class="border-b border-gray-200">
                    <h3 class="px-5 py-4 text-xl font-semibold text-gray-800">
                        <i class="fas fa-bell text-yellow-500 mr-2"></i>
                        التنبيهات والملاحظات المهمة
                    </h3>
                </div>
                <div id="expiration-notifications" class="p-5 space-y-4">
                    <!-- Expiration notifications will be loaded here -->
                    <div class="p-4 rounded-xl bg-red-50 border border-red-100 animate-fade-in">
                        <div class="flex gap-3">
                            <div class="w-10 h-10 rounded-lg bg-red-500/10 text-red-600 flex items-center justify-center">
                                <i class="fas fa-passport text-lg"></i>
                            </div>
                            <div>
                                <p class="font-medium text-red-700">تاريخ انتهاء جواز السفر</p>
                                <p class="text-sm text-gray-600">جابر يوسف علي | تنتهي في: 2023-12-25 (بعد 5 أيام)</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-4 rounded-xl bg-yellow-50 border border-yellow-100 animate-fade-in">
                        <div class="flex gap-3">
                            <div class="w-10 h-10 rounded-lg bg-yellow-500/10 text-yellow-600 flex items-center justify-center">
                                <i class="fas fa-id-card text-lg"></i>
                            </div>
                            <div>
                                <p class="font-medium text-yellow-700">تاريخ انتهاء رخصة العمل</p>
                                <p class="text-sm text-gray-600">سعود فهد إبراهيم | تنتهي في: 2024-01-15 (بعد 26 يومًا)</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-4 rounded-xl bg-blue-50 border border-blue-100 animate-fade-in">
                        <div class="flex gap-3">
                            <div class="w-10 h-10 rounded-lg bg-blue-500/10 text-blue-600 flex items-center justify-center">
                                <i class="fas fa-calendar-check text-lg"></i>
                            </div>
                            <div>
                                <p class="font-medium text-blue-700">ذكرى سنوية للموظفين</p>
                                <p class="text-sm text-gray-600">لدى ٥ موظفين مرور عام في الشهر القادم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Employee Section -->
        <div id="employee-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-blue-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">المعلومات الشخصية للموظفين</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-employee-btn" class="bg-primary hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-user-plus"></i>
                        إضافة موظف جديد
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-filter"></i>
                        تصفية الموظفين
                    </button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-12 gap-5 mb-6">
                <!-- Search Section -->
                <div class="md:col-span-8 flex">
                    <input type="text" id="employee-search" placeholder="بحث الموظفين بالاسم أو الرقم" class="flex-grow border-l-0 rounded-l-lg border border-gray-300 px-4 py-2 focus:outline-none focus:border-primary">
                    <button id="search-btn" class="bg-primary text-white px-5 rounded-r-lg hover:bg-blue-700 transition-all">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <!-- Actions Section -->
                <div class="md:col-span-4 flex gap-2 justify-end">
                    <button id="print-employees-btn" class="bg-white border border-gray-300 rounded-lg px-4 h-full hover:bg-gray-50 flex items-center gap-2 transition-all">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button id="export-employees-btn" class="bg-white border border-gray-300 rounded-lg px-4 h-full hover:bg-gray-50 flex items-center gap-2 transition-all">
                        <i class="fas fa-file-export"></i>
                        تصدير
                    </button>
                </div>
            </div>
            
            <!-- Employee List -->
            <div id="employee-list" class="space-y-4">
                <!-- Employee Card 1 -->
                <div class="employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md">
                    <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                        <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                    </div>
                    <div class="p-4 sm:p-6 flex-grow">
                        <div class="flex flex-wrap justify-between">
                            <h3 class="text-xl font-bold text-gray-800">طارق عبدالله خالد</h3>
                            <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">نشيط</span>
                        </div>
                        
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-id-card text-gray-400 w-5"></i>
                                <span class="font-medium">الرقم المدني: 293012301230</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                                <span>+965 50012345</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-building text-gray-400 w-5"></i>
                                <span>الأقسام: تقنية المعلومات</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-briefcase text-gray-400 w-5"></i>
                                <span>المسمى الوظيفي: مطور ويب</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-4">
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-user-circle text-blue-600"></i>
                                عرض الملف الشخصي
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-envelope text-yellow-600"></i>
                                إرسال رسالة
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-money-bill-wave text-green-600"></i>
                                الرواتب
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Card 2 -->
                <div class="employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md">
                    <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                        <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                    </div>
                    <div class="p-4 sm:p-6 flex-grow">
                        <div class="flex flex-wrap justify-between">
                            <h3 class="text-xl font-bold text-gray-800">نورة أحمد سعد</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">في إجازة</span>
                        </div>
                        
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-id-card text-gray-400 w-5"></i>
                                <span class="font-medium">الرقم المدني: 293082301230</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                                <span>+965 50054321</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-building text-gray-400 w-5"></i>
                                <span>الأقسام: الموارد البشرية</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-briefcase text-gray-400 w-5"></i>
                                <span>المسمى الوظيفي: أخصائي شؤون الموظفين</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-4">
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-user-circle text-blue-600"></i>
                                عرض الملف الشخصي
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-envelope text-yellow-600"></i>
                                إرسال رسالة
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-calendar-alt text-purple-600"></i>
                                حالة الإجازة
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Loading indicator -->
                <p class="text-gray-600 text-center text-lg py-8 animate-pulse italic">
                    جاري تحميل بيانات الموظفين...
                </p>
            </div>
        </div>
        
        <!-- Attendance Section -->
        <div id="attendance-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800 mb-6 border-b-2 pb-4 border-primary">الحضور والانصراف</h2>
            
            <!-- Attendance Today Stats -->
            <div class="flex flex-col md:flex-row gap-5 mb-8">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-xl w-full md:w-1/2">
                    <div class="flex justify-between">
                        <div>
                            <p class="text-sm opacity-90">حضور اليوم</p>
                            <p class="text-3xl font-bold mt-1">189 <span class="text-lg font-medium">/ 230</span></p>
                            <p class="text-sm mt-2 flex items-center gap-1">89% الحضور</p>
                        </div>
                        <div class="bg-white/20 p-4 rounded-full">
                            <i class="fas fa-user-clock text-3xl"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="h-2 bg-white/40 rounded-full">
                            <div class="h-2 bg-white rounded-full" style="width: 89%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 border rounded-xl p-6 w-full md:w-1/2">
                    <p class="text-lg font-semibold mb-3">تسجيل الدخول السريع</p>
                    <div class="bg-blue-50 border border-blue-100 rounded-xl p-4">
                        <div class="flex flex-col sm:flex-row sm:items-center gap-3 mb-3">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">اختر الموظف:</label>
                                <select id="attendance-employee-select" class="w-full border rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary/50">
                                    <option>-- اختر الموظف --</option>
                                    <option>طارق عبدالله خالد</option>
                                    <option>نورة أحمد سعد</option>
                                    <option>فهد عبدالعزيز محمد</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button id="clock-in-out-btn" class="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-600 text-white font-medium py-2 px-5 rounded-lg transition duration-300 shadow-md w-full sm:w-auto">
                                <i class="fas fa-clock"></i>
                                تسجيل الدخول / الخروج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Records -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">سجلات الحضور</h3>
                    <div>
                        <select id="month-select" class="text-sm border p-2 rounded">
                            <option>ديسمبر 2023</option>
                            <option selected>نوفمبر 2023</option>
                            <option>أكتوبر 2023</option>
                        </select>
                    </div>
                </div>
                <div id="attendance-list" class="p-4 sm:p-5">
                    <ul class="divide-y divide-gray-100">
                        <li class="py-4">
                            <div class="flex flex-col sm:flex-row sm:items-center">
                                <div class="flex-1 mb-3 sm:mb-0">
                                    <h4 class="font-medium text-gray-800">طارق عبدالله خالد</h4>
                                    <p class="text-sm text-gray-500 mt-1">الأقسام: تقنية المعلومات</p>
                                </div>
                                <div class="flex items-center gap-4 sm:gap-8">
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الدخول</p>
                                        <p class="font-medium text-green-600 mt-0.5">08:45 صباحًا</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الخروج</p>
                                        <p class="font-medium text-red-600 mt-0.5">05:12 مساءً</p>
                                    </div>
                                    <div class="bg-blue-50 px-3 py-1 rounded-full">
                                        <span class="text-blue-600 text-sm">8 ساعات 27 د</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        <li class="py-4">
                            <div class="flex flex-col sm:flex-row sm:items-center">
                                <div class="flex-1 mb-3 sm:mb-0">
                                    <h4 class="font-medium text-gray-800">نورة أحمد سعد</h4>
                                    <p class="text-sm text-gray-500 mt-1">الأقسام: الموارد البشرية</p>
                                </div>
                                <div class="flex items-center gap-4 sm:gap-8">
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الدخول</p>
                                        <p class="font-medium text-green-600 mt-0.5">08:32 صباحًا</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الخروج</p>
                                        <p class="font-medium text-gray-400 mt-0.5">--</p>
                                    </div>
                                    <div class="bg-yellow-50 px-3 py-1 rounded-full">
                                        <span class="text-yellow-600 text-sm">لا تزال موجودة</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <p class="text-gray-600 text-center py-5 italic">
                        جارٍ تحميل المزيد من سجلات الحضور...
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Leaves Section -->
        <div id="leaves-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-green-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">إدارة الإجازات</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-leave-btn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-calendar-plus"></i>
                        طلب إجازة جديد
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-filter"></i>
                        تصفية الإجازات
                    </button>
                </div>
            </div>

            <!-- Leave Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-5 mb-8">
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-calendar-check text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">45</p>
                    <p class="text-sm mt-1">إجازات معتمدة</p>
                </div>
                <div class="bg-gradient-to-br from-yellow-500 to-yellow-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">12</p>
                    <p class="text-sm mt-1">في انتظار الموافقة</p>
                </div>
                <div class="bg-gradient-to-br from-red-500 to-red-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-times-circle text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">3</p>
                    <p class="text-sm mt-1">إجازات مرفوضة</p>
                </div>
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-user-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">22</p>
                    <p class="text-sm mt-1">موظفون في إجازة</p>
                </div>
            </div>

            <!-- Leave Requests List -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4">
                    <h3 class="text-lg font-semibold text-gray-800">طلبات الإجازات</h3>
                </div>
                <div id="leaves-list" class="p-4 sm:p-5">
                    <div class="space-y-4">
                        <!-- Leave Request 1 -->
                        <div class="border border-gray-100 rounded-xl p-4 hover:shadow-md transition-all">
                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">أحمد محمد علي</h4>
                                    <p class="text-sm text-gray-500 mt-1">إجازة سنوية - 5 أيام</p>
                                    <p class="text-sm text-gray-500">من 2024-01-15 إلى 2024-01-19</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">في انتظار الموافقة</span>
                                    <div class="flex gap-2">
                                        <button class="bg-green-100 hover:bg-green-200 text-green-700 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-check"></i> موافقة
                                        </button>
                                        <button class="bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-times"></i> رفض
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Leave Request 2 -->
                        <div class="border border-gray-100 rounded-xl p-4 hover:shadow-md transition-all">
                            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">فاطمة سعد الدين</h4>
                                    <p class="text-sm text-gray-500 mt-1">إجازة مرضية - 3 أيام</p>
                                    <p class="text-sm text-gray-500">من 2024-01-10 إلى 2024-01-12</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">معتمدة</span>
                                    <div class="flex gap-2">
                                        <button class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg text-sm">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p class="text-gray-600 text-center py-5 italic">
                        جارٍ تحميل المزيد من طلبات الإجازات...
                    </p>
                </div>
            </div>
        </div>

        <!-- Salaries Section -->
        <div id="salaries-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-purple-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">إدارة الرواتب</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-salary-btn" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        إضافة راتب
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-calculator"></i>
                        حساب الرواتب
                    </button>
                </div>
            </div>

            <!-- Salary Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-5 mb-8">
                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-money-bill-wave text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">125,000</p>
                    <p class="text-sm mt-1">إجمالي الرواتب (د.ك)</p>
                </div>
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">198</p>
                    <p class="text-sm mt-1">رواتب مدفوعة</p>
                </div>
                <div class="bg-gradient-to-br from-orange-500 to-orange-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">32</p>
                    <p class="text-sm mt-1">رواتب معلقة</p>
                </div>
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl text-center">
                    <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                    <p class="text-2xl font-bold">542</p>
                    <p class="text-sm mt-1">متوسط الراتب (د.ك)</p>
                </div>
            </div>

            <!-- Salary List -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">كشف الرواتب</h3>
                    <div class="flex gap-2">
                        <select class="text-sm border p-2 rounded">
                            <option>يناير 2024</option>
                            <option selected>ديسمبر 2023</option>
                            <option>نوفمبر 2023</option>
                        </select>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القسم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الراتب الأساسي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البدلات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخصومات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصافي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300"></div>
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">طارق عبدالله خالد</div>
                                            <div class="text-sm text-gray-500">293012301230</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">تقنية المعلومات</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">650 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">150 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">50 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">750 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        مدفوع
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300"></div>
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">نورة أحمد سعد</div>
                                            <div class="text-sm text-gray-500">293082301230</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الموارد البشرية</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">580 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">120 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">35 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">665 د.ك</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        معلق
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-5 py-4 text-center">
                    <p class="text-gray-600 italic">
                        جارٍ تحميل المزيد من بيانات الرواتب...
                    </p>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add Employee Modal -->
    <div id="add-employee-modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 id="form-title" class="text-2xl font-bold text-gray-800 mb-6">إضافة موظف جديد</h2>

            <form id="employee-form" class="space-y-4">
                <input type="hidden" id="employee-id-field" name="employee-id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="first-name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الأول</label>
                        <input type="text" id="first-name" name="first-name" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="last-name" class="block text-sm font-medium text-gray-700 mb-1">اسم العائلة</label>
                        <input type="text" id="last-name" name="last-name" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="civil-id" class="block text-sm font-medium text-gray-700 mb-1">الرقم المدني</label>
                        <input type="text" id="civil-id" name="civil-id" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="file-number" class="block text-sm font-medium text-gray-700 mb-1">رقم الملف</label>
                        <input type="text" id="file-number" name="file-number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-1">القسم</label>
                        <select id="department" name="department" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر القسم</option>
                            <option value="تقنية المعلومات">تقنية المعلومات</option>
                            <option value="الموارد البشرية">الموارد البشرية</option>
                            <option value="المالية">المالية</option>
                            <option value="التسويق">التسويق</option>
                            <option value="المبيعات">المبيعات</option>
                            <option value="الإدارة">الإدارة</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="position" class="block text-sm font-medium text-gray-700 mb-1">المسمى الوظيفي</label>
                        <input type="text" id="position" name="position" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="hire-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ التوظيف</label>
                        <input type="date" id="hire-date" name="hire-date" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="personal-phone-number" class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف الشخصي</label>
                        <input type="tel" id="personal-phone-number" name="personal-phone-number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="work-phone-number" class="block text-sm font-medium text-gray-700 mb-1">رقم هاتف العمل</label>
                        <input type="tel" id="work-phone-number" name="work-phone-number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="passport-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء جواز السفر</label>
                        <input type="date" id="passport-expiry-date" name="passport-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="residence-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء الإقامة</label>
                        <input type="date" id="residence-expiry-date" name="residence-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="license-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء رخصة العمل</label>
                        <input type="date" id="license-expiry-date" name="license-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="health-card-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ انتهاء البطاقة الصحية</label>
                        <input type="date" id="health-card-expiry-date" name="health-card-expiry-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div class="flex justify-end gap-3 pt-4">
                    <button type="button" class="close-button bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-6 rounded-lg transition-all">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-primary hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-all">
                        حفظ البيانات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Leave Modal -->
    <div id="add-leave-modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 class="text-2xl font-bold text-gray-800 mb-6">طلب إجازة جديد</h2>

            <form id="leave-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="leave-employee" class="block text-sm font-medium text-gray-700 mb-1">الموظف</label>
                        <select id="leave-employee" name="leave-employee" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر الموظف</option>
                            <option value="طارق عبدالله خالد">طارق عبدالله خالد</option>
                            <option value="نورة أحمد سعد">نورة أحمد سعد</option>
                        </select>
                    </div>
                    <div>
                        <label for="leave-type" class="block text-sm font-medium text-gray-700 mb-1">نوع الإجازة</label>
                        <select id="leave-type" name="leave-type" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                            <option value="">اختر نوع الإجازة</option>
                            <option value="سنوية">إجازة سنوية</option>
                            <option value="مرضية">إجازة مرضية</option>
                            <option value="طارئة">إجازة طارئة</option>
                            <option value="أمومة">إجازة أمومة</option>
                            <option value="حج">إجازة حج</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="leave-start-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية</label>
                        <input type="date" id="leave-start-date" name="leave-start-date" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                    <div>
                        <label for="leave-end-date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ النهاية</label>
                        <input type="date" id="leave-end-date" name="leave-end-date" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                    </div>
                </div>

                <div>
                    <label for="leave-reason" class="block text-sm font-medium text-gray-700 mb-1">سبب الإجازة</label>
                    <textarea id="leave-reason" name="leave-reason" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50" placeholder="اكتب سبب طلب الإجازة..."></textarea>
                </div>

                <div class="flex justify-end gap-3 pt-4">
                    <button type="button" class="close-button bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-6 rounded-lg transition-all">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-all">
                        إرسال الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer Section -->
    <footer class="bg-primary text-white mt-8 pt-8 pb-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between gap-8">
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">نظام إدارة الموارد البشريّة</h3>
                    <p class="text-sm opacity-80 leading-relaxed">
                        نظام متكامل لإدارة الموارد البشرية للشركات في دولة الكويت، يشمل نظام الحضور، الرواتب، الإجازات، وشؤون ومعلومات الموظفين.
                    </p>
                </div>
                
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">روابط سريعة</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">لوحة التحكم</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الموظفون</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الحضور والانصراف</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الإجازات</a></li>
                    </ul>
                </div>
                
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">الدعم الفني</h3>
                    <ul class="space-y-3">
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-map-marker-alt mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80">مدينة الكويت - برج التحرير</span>
                        </li>
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-phone-alt mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80">+965 22212345</span>
                        </li>
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-envelope mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-sm opacity-80">
                <p>&copy; 2023 نظام إدارة الموارد البشريّّة - جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
    
    <script>
        // Toggle mobile menu
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const nav = document.getElementById('main-nav');
            nav.classList.toggle('hidden');
            if(!nav.classList.contains('hidden')) {
                nav.classList.add('block', 'py-3', 'px-3', 'bg-white/10', 'rounded-lg', 'mt-2');
            }
        });
        
        // Set current date
        const date = new Date();
        const formattedDate = date.toLocaleDateString('ar-KW', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('current-date').textContent = 'اليوم: ' + formattedDate;
        
        // Enhanced tab switching with proper functionality
        document.querySelectorAll('nav ul li a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Get target tab
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all nav items
                document.querySelectorAll('nav ul li a').forEach(el => {
                    el.parentElement.classList.remove('bg-white/20');
                    el.classList.remove('font-bold', 'text-blue-100');
                });

                // Add active state to clicked item
                this.classList.add('font-bold', 'text-blue-100');
                this.parentElement.classList.add('bg-white/20');

                // Show target tab content
                if (targetTab) {
                    showTab(targetTab);
                }

                // Close mobile menu if open
                const nav = document.getElementById('main-nav');
                if (nav.classList.contains('block')) {
                    nav.classList.add('hidden');
                    nav.classList.remove('block', 'py-3', 'px-3', 'bg-white/10', 'rounded-lg', 'mt-2');
                }
            });
        });

        // Active dashboard nav item by default
        document.querySelector('[data-tab="dashboard-section"]').classList.add('font-bold', 'text-blue-100');
        document.querySelector('[data-tab="dashboard-section"]').parentElement.classList.add('bg-white/20');
        
        // Enhanced modal functionality
        document.getElementById('add-employee-btn').addEventListener('click', function() {
            document.getElementById('employee-form').reset();
            document.getElementById('form-title').textContent = 'إضافة موظف جديد';
            document.getElementById('add-employee-modal').style.display = 'flex';
        });

        // Add Leave button functionality
        document.getElementById('add-leave-btn').addEventListener('click', function() {
            document.getElementById('leave-form').reset();
            document.getElementById('add-leave-modal').style.display = 'flex';
        });

        // Close modals
        document.querySelectorAll('.close-button').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
        
        // Function to show message
        function showMessage(message, type = 'success') {
            const messageBox = document.getElementById('message-box');
            messageBox.textContent = message;
            messageBox.className = `p-3 rounded-lg text-white mb-4 text-center animate-fade-in ${type === 'error' ? 'bg-red-500' : 'bg-green-500'}`;
            messageBox.style.display = 'block';
            
            setTimeout(() => {
                messageBox.style.display = 'none';
            }, 5000);
        }

        // Local Storage Database System
        let employees = JSON.parse(localStorage.getItem('hrms_employees') || '[]');
        let leaves = JSON.parse(localStorage.getItem('hrms_leaves') || '[]');
        let attendance = JSON.parse(localStorage.getItem('hrms_attendance') || '[]');
        let salaries = JSON.parse(localStorage.getItem('hrms_salaries') || '[]');

        // Initialize with sample data if empty
        function initializeSampleData() {
            if (employees.length === 0) {
                employees = [
                    {
                        id: 'emp001',
                        first_name: 'طارق',
                        last_name: 'عبدالله خالد',
                        email: '<EMAIL>',
                        civil_id: '293012301230',
                        file_number: 'EMP001',
                        department: 'تقنية المعلومات',
                        position: 'مطور ويب',
                        hire_date: '2022-01-15',
                        personal_phone_number: '+965 50012345',
                        work_phone_number: '+965 22334455',
                        passport_expiry_date: '2025-06-15',
                        residence_expiry_date: '2024-12-31',
                        license_expiry_date: '2024-08-20',
                        health_card_expiry_date: '2024-11-30',
                        status: 'active',
                        created_at: '2022-01-15T08:00:00.000Z',
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp002',
                        first_name: 'نورة',
                        last_name: 'أحمد سعد',
                        email: '<EMAIL>',
                        civil_id: '293082301230',
                        file_number: 'EMP002',
                        department: 'الموارد البشرية',
                        position: 'أخصائي شؤون الموظفين',
                        hire_date: '2021-09-10',
                        personal_phone_number: '+965 50054321',
                        work_phone_number: '+965 22334466',
                        passport_expiry_date: '2026-03-20',
                        residence_expiry_date: '2024-09-15',
                        license_expiry_date: '2024-07-10',
                        health_card_expiry_date: '2024-10-25',
                        status: 'active',
                        created_at: '2021-09-10T08:00:00.000Z',
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp003',
                        first_name: 'فهد',
                        last_name: 'عبدالعزيز محمد',
                        email: '<EMAIL>',
                        civil_id: '293092301230',
                        file_number: 'EMP003',
                        department: 'المالية',
                        position: 'محاسب أول',
                        hire_date: '2020-03-01',
                        personal_phone_number: '+965 50098765',
                        work_phone_number: '+965 22334477',
                        passport_expiry_date: '2025-12-10',
                        residence_expiry_date: '2024-06-30',
                        license_expiry_date: '2024-05-15',
                        health_card_expiry_date: '2024-08-20',
                        status: 'active',
                        created_at: '2020-03-01T08:00:00.000Z',
                        updated_at: new Date().toISOString()
                    }
                ];

                leaves = [
                    {
                        id: 'leave001',
                        employee: 'طارق عبدالله خالد',
                        type: 'سنوية',
                        start_date: '2024-02-01',
                        end_date: '2024-02-05',
                        reason: 'إجازة سنوية للراحة',
                        status: 'approved',
                        created_at: '2024-01-15T10:00:00.000Z'
                    },
                    {
                        id: 'leave002',
                        employee: 'نورة أحمد سعد',
                        type: 'مرضية',
                        start_date: '2024-01-20',
                        end_date: '2024-01-22',
                        reason: 'إجازة مرضية',
                        status: 'pending',
                        created_at: '2024-01-18T09:00:00.000Z'
                    }
                ];

                // Generate sample attendance for today
                const today = new Date().toISOString().split('T')[0];
                attendance = [
                    {
                        id: 'att001',
                        employee_id: 'emp001',
                        employee_name: 'طارق عبدالله خالد',
                        date: today,
                        check_in: '08:45',
                        check_out: '17:12',
                        hours_worked: 8.45,
                        status: 'present'
                    },
                    {
                        id: 'att002',
                        employee_id: 'emp002',
                        employee_name: 'نورة أحمد سعد',
                        date: today,
                        check_in: '08:32',
                        check_out: null,
                        hours_worked: 0,
                        status: 'present'
                    },
                    {
                        id: 'att003',
                        employee_id: 'emp003',
                        employee_name: 'فهد عبدالعزيز محمد',
                        date: today,
                        check_in: '09:15',
                        check_out: '17:30',
                        hours_worked: 8.25,
                        status: 'present'
                    }
                ];

                saveData();
            }
        }

        // Generate unique ID
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // Save data to localStorage
        function saveData() {
            localStorage.setItem('hrms_employees', JSON.stringify(employees));
            localStorage.setItem('hrms_leaves', JSON.stringify(leaves));
            localStorage.setItem('hrms_attendance', JSON.stringify(attendance));
            localStorage.setItem('hrms_salaries', JSON.stringify(salaries));
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', () => {
            try {
                // Initialize sample data if needed
                initializeSampleData();

                // Set user display
                document.getElementById('user-id-display').textContent = 'مرحباً بك في النظام';

                // Load initial data
                loadEmployees();
                loadLeaves();
                loadAttendance();
                updateDashboardStats();

                // Show dashboard by default
                showTab('dashboard-section');

            } catch (error) {
                console.error("Application initialization error:", error);
                showMessage(`حدث خطأ في تهيئة النظام: ${error.message}`, 'error');
            }

            // Add Employee Form Submission
            const employeeForm = document.getElementById('employee-form');
            if (employeeForm) {
                employeeForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    const employeeData = {
                        id: document.getElementById('employee-id-field').value || generateId(),
                        first_name: document.getElementById('first-name').value,
                        last_name: document.getElementById('last-name').value,
                        email: document.getElementById('email').value,
                        file_number: document.getElementById('file-number').value,
                        civil_id: document.getElementById('civil-id').value,
                        department: document.getElementById('department').value,
                        position: document.getElementById('position').value,
                        hire_date: document.getElementById('hire-date').value,
                        residence_expiry_date: document.getElementById('residence-expiry-date').value,
                        passport_expiry_date: document.getElementById('passport-expiry-date').value,
                        personal_phone_number: document.getElementById('personal-phone-number').value,
                        work_phone_number: document.getElementById('work-phone-number').value,
                        license_expiry_date: document.getElementById('license-expiry-date').value,
                        health_card_expiry_date: document.getElementById('health-card-expiry-date').value,
                        status: 'active',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };

                    try {
                        const existingIndex = employees.findIndex(emp => emp.id === employeeData.id);

                        if (existingIndex !== -1) {
                            // Update existing employee
                            employees[existingIndex] = { ...employees[existingIndex], ...employeeData };
                            showMessage('تم تحديث بيانات الموظف بنجاح');
                        } else {
                            // Add new employee
                            employees.push(employeeData);
                            showMessage('تم إضافة الموظف بنجاح');
                        }

                        // Save to localStorage
                        saveData();

                        // Close modal and refresh list
                        document.getElementById('add-employee-modal').style.display = 'none';
                        loadEmployees();
                        updateDashboardStats();

                    } catch (error) {
                        console.error("Error saving employee:", error);
                        showMessage(`حدث خطأ أثناء حفظ البيانات: ${error.message}`, 'error');
                    }
                });
            }

            // Add Leave Form Submission
            const leaveForm = document.getElementById('leave-form');
            if (leaveForm) {
                leaveForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    const leaveData = {
                        id: generateId(),
                        employee: document.getElementById('leave-employee').value,
                        type: document.getElementById('leave-type').value,
                        start_date: document.getElementById('leave-start-date').value,
                        end_date: document.getElementById('leave-end-date').value,
                        reason: document.getElementById('leave-reason').value,
                        status: 'pending',
                        created_at: new Date().toISOString()
                    };

                    try {
                        leaves.push(leaveData);
                        saveData();

                        showMessage('تم إرسال طلب الإجازة بنجاح');
                        document.getElementById('add-leave-modal').style.display = 'none';
                        loadLeaves();
                        updateDashboardStats();

                    } catch (error) {
                        console.error("Error saving leave:", error);
                        showMessage(`حدث خطأ أثناء حفظ طلب الإجازة: ${error.message}`, 'error');
                    }
                });
            }

        });

        // Function to load employees
        function loadEmployees() {
            const employeeListDiv = document.getElementById('employee-list');
            if (!employeeListDiv) return;

            employeeListDiv.innerHTML = ''; // Clear current list

            if (employees.length === 0) {
                employeeListDiv.innerHTML = `
                    <div class="bg-gray-50 p-8 rounded-xl text-center">
                        <i class="fas fa-users-slash text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">لا يوجد موظفون مسجلون بعد</p>
                        <button onclick="document.getElementById('add-employee-modal').style.display='flex'" class="mt-4 bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all">
                            إضافة أول موظف
                        </button>
                    </div>
                `;
                return;
            }

            employees.forEach(employee => {
                employeeListDiv.appendChild(createEmployeeCard(employee.id, employee));
            });

            // Update attendance select dropdown
            populateAttendanceSelect();
        }

        // Function to load leaves
        function loadLeaves() {
            const leavesListDiv = document.getElementById('leaves-list');
            if (!leavesListDiv) return;

            // Update leaves count in dashboard
            const leavesCount = leaves.filter(leave => leave.status === 'approved').length;
            const pendingCount = leaves.filter(leave => leave.status === 'pending').length;

            // Update leave statistics if elements exist
            const employeesOnLeaveElement = document.getElementById('employees-on-leave-count');
            if (employeesOnLeaveElement) {
                employeesOnLeaveElement.textContent = leavesCount;
            }
        }

        // Function to load attendance
        function loadAttendance() {
            // Update attendance count in dashboard
            const todayAttendance = attendance.filter(att => {
                const today = new Date().toDateString();
                const attDate = new Date(att.date).toDateString();
                return today === attDate;
            });

            const attendanceCountElement = document.getElementById('attendance-count');
            if (attendanceCountElement) {
                attendanceCountElement.textContent = todayAttendance.length;
            }
        }

        // Function to create employee card
        function createEmployeeCard(id, employee) {
            const card = document.createElement('div');
            card.className = 'employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md';
            card.innerHTML = `
                <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                    <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                </div>
                <div class="p-4 sm:p-6 flex-grow">
                    <div class="flex flex-wrap justify-between">
                        <h3 class="text-xl font-bold text-gray-800">${employee.first_name || ''} ${employee.last_name || ''}</h3>
                        <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">${employee.status === 'active' ? 'نشيط' : 'غير نشط'}</span>
                    </div>
                    
                    <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-id-card text-gray-400 w-5"></i>
                            <span class="font-medium">الرقم المدني: ${employee.civil_id || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                            <span>${employee.personal_phone_number || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-building text-gray-400 w-5"></i>
                            <span>الأقسام: ${employee.department || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-briefcase text-gray-400 w-5"></i>
                            <span>المسمى الوظيفي: ${employee.position || 'غير محدد'}</span>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="viewEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-user-circle text-blue-600"></i>
                            عرض الملف الشخصي
                        </button>
                        <button onclick="editEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-edit text-yellow-600"></i>
                            تعديل البيانات
                        </button>
                        <button onclick="deleteEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-trash-alt text-red-600"></i>
                            حذف الموظف
                        </button>
                    </div>
                </div>
            `;
            return card;
        }

        // Function to edit employee
        window.editEmployee = function(id) {
            const employee = employees.find(emp => emp.id === id);

            if (employee) {
                document.getElementById('form-title').textContent = 'تعديل بيانات الموظف';
                document.getElementById('employee-id-field').value = id;

                // Set form values
                document.getElementById('first-name').value = employee.first_name || '';
                document.getElementById('last-name').value = employee.last_name || '';
                document.getElementById('email').value = employee.email || '';
                document.getElementById('file-number').value = employee.file_number || '';
                document.getElementById('civil-id').value = employee.civil_id || '';
                document.getElementById('department').value = employee.department || '';
                document.getElementById('position').value = employee.position || '';
                document.getElementById('hire-date').value = employee.hire_date || '';
                document.getElementById('residence-expiry-date').value = employee.residence_expiry_date || '';
                document.getElementById('passport-expiry-date').value = employee.passport_expiry_date || '';
                document.getElementById('license-expiry-date').value = employee.license_expiry_date || '';
                document.getElementById('health-card-expiry-date').value = employee.health_card_expiry_date || '';
                document.getElementById('personal-phone-number').value = employee.personal_phone_number || '';
                document.getElementById('work-phone-number').value = employee.work_phone_number || '';

                document.getElementById('add-employee-modal').style.display = 'flex';
            } else {
                showMessage('لم يتم العثور على بيانات الموظف', 'error');
            }
        };

        // Function to delete employee
        window.deleteEmployee = function(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                try {
                    employees = employees.filter(emp => emp.id !== id);
                    saveData();
                    showMessage('تم حذف الموظف بنجاح');
                    loadEmployees();
                    updateDashboardStats();
                } catch (error) {
                    showMessage('حدث خطأ أثناء حذف الموظف', 'error');
                }
            }
        };

        // Function to view employee details
        window.viewEmployee = function(id) {
            const employee = employees.find(emp => emp.id === id);
            if (employee) {
                alert(`تفاصيل الموظف:\n\nالاسم: ${employee.first_name} ${employee.last_name}\nالقسم: ${employee.department}\nالمنصب: ${employee.position}\nالرقم المدني: ${employee.civil_id}\nالهاتف: ${employee.personal_phone_number}`);
            }
        };

        // Function to update dashboard stats
        function updateDashboardStats() {
            const totalEmployeesElement = document.getElementById('total-employees-count');
            if (totalEmployeesElement) {
                totalEmployeesElement.textContent = employees.length;
            }

            const leavesCountElement = document.getElementById('leaves-count');
            if (leavesCountElement) {
                leavesCountElement.textContent = leaves.filter(leave => leave.status === 'approved').length;
            }

            const attendanceCountElement = document.getElementById('attendance-count');
            if (attendanceCountElement) {
                const today = new Date().toDateString();
                const todayAttendance = attendance.filter(att => {
                    const attDate = new Date(att.date).toDateString();
                    return today === attDate;
                });
                attendanceCountElement.textContent = todayAttendance.length;
            }
        }

        // Function to switch tabs
        function showTab(tabId) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }
        }

        // Search functionality
        function searchEmployees(query) {
            if (!query.trim()) {
                loadEmployees();
                return;
            }

            const filteredEmployees = employees.filter(employee => {
                const fullName = `${employee.first_name} ${employee.last_name}`.toLowerCase();
                const civilId = employee.civil_id || '';
                const department = employee.department || '';
                const position = employee.position || '';

                return fullName.includes(query.toLowerCase()) ||
                       civilId.includes(query) ||
                       department.toLowerCase().includes(query.toLowerCase()) ||
                       position.toLowerCase().includes(query.toLowerCase());
            });

            displayFilteredEmployees(filteredEmployees);
        }

        // Display filtered employees
        function displayFilteredEmployees(filteredEmployees) {
            const employeeListDiv = document.getElementById('employee-list');
            if (!employeeListDiv) return;

            employeeListDiv.innerHTML = '';

            if (filteredEmployees.length === 0) {
                employeeListDiv.innerHTML = `
                    <div class="bg-gray-50 p-8 rounded-xl text-center">
                        <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">لم يتم العثور على نتائج مطابقة</p>
                    </div>
                `;
                return;
            }

            filteredEmployees.forEach(employee => {
                employeeListDiv.appendChild(createEmployeeCard(employee.id, employee));
            });
        }

        // Export employees to CSV
        function exportEmployeesToCSV() {
            if (employees.length === 0) {
                showMessage('لا توجد بيانات للتصدير', 'error');
                return;
            }

            const headers = ['الاسم الأول', 'اسم العائلة', 'الرقم المدني', 'القسم', 'المنصب', 'الهاتف', 'البريد الإلكتروني', 'تاريخ التوظيف'];
            const csvContent = [
                headers.join(','),
                ...employees.map(emp => [
                    emp.first_name || '',
                    emp.last_name || '',
                    emp.civil_id || '',
                    emp.department || '',
                    emp.position || '',
                    emp.personal_phone_number || '',
                    emp.email || '',
                    emp.hire_date || ''
                ].join(','))
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `employees_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('تم تصدير البيانات بنجاح');
        }

        // Print employees list
        function printEmployeesList() {
            if (employees.length === 0) {
                showMessage('لا توجد بيانات للطباعة', 'error');
                return;
            }

            const printWindow = window.open('', '_blank');
            const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>قائمة الموظفين</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        h1 { text-align: center; color: #2563eb; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .date { text-align: left; margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>نظام إدارة الموارد البشرية - الكويت</h1>
                        <h2>قائمة الموظفين</h2>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الرقم المدني</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>الهاتف</th>
                                <th>تاريخ التوظيف</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${employees.map(emp => `
                                <tr>
                                    <td>${emp.first_name} ${emp.last_name}</td>
                                    <td>${emp.civil_id || ''}</td>
                                    <td>${emp.department || ''}</td>
                                    <td>${emp.position || ''}</td>
                                    <td>${emp.personal_phone_number || ''}</td>
                                    <td>${emp.hire_date || ''}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    <div class="date">
                        تاريخ الطباعة: ${new Date().toLocaleDateString('ar-KW')}
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        // Add event listeners for search and actions
        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality
            const searchInput = document.getElementById('employee-search');
            const searchBtn = document.getElementById('search-btn');

            if (searchInput && searchBtn) {
                searchInput.addEventListener('input', function() {
                    searchEmployees(this.value);
                });

                searchBtn.addEventListener('click', function() {
                    searchEmployees(searchInput.value);
                });

                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchEmployees(this.value);
                    }
                });
            }

            // Export functionality
            const exportBtn = document.getElementById('export-employees-btn');
            if (exportBtn) {
                exportBtn.addEventListener('click', exportEmployeesToCSV);
            }

            // Print functionality
            const printBtn = document.getElementById('print-employees-btn');
            if (printBtn) {
                printBtn.addEventListener('click', printEmployeesList);
            }

            // Clock in/out functionality
            const clockBtn = document.getElementById('clock-in-out-btn');
            if (clockBtn) {
                clockBtn.addEventListener('click', handleClockInOut);
            }
        });

        // Handle clock in/out
        function handleClockInOut() {
            const selectedEmployee = document.getElementById('attendance-employee-select').value;
            if (!selectedEmployee || selectedEmployee === '-- اختر الموظف --') {
                showMessage('يرجى اختيار الموظف أولاً', 'error');
                return;
            }

            const today = new Date().toISOString().split('T')[0];
            const currentTime = new Date().toLocaleTimeString('en-GB', { hour12: false }).substring(0, 5);

            // Find existing attendance record for today
            const existingAttendance = attendance.find(att =>
                att.employee_name === selectedEmployee && att.date === today
            );

            if (existingAttendance) {
                if (!existingAttendance.check_out) {
                    // Clock out
                    existingAttendance.check_out = currentTime;
                    const checkIn = new Date(`2000-01-01 ${existingAttendance.check_in}`);
                    const checkOut = new Date(`2000-01-01 ${currentTime}`);
                    const hoursWorked = (checkOut - checkIn) / (1000 * 60 * 60);
                    existingAttendance.hours_worked = Math.round(hoursWorked * 100) / 100;

                    showMessage(`تم تسجيل خروج ${selectedEmployee} في ${currentTime}`);
                } else {
                    showMessage('تم تسجيل الدخول والخروج لهذا الموظف اليوم', 'error');
                    return;
                }
            } else {
                // Clock in
                const newAttendance = {
                    id: generateId(),
                    employee_id: employees.find(emp => `${emp.first_name} ${emp.last_name}` === selectedEmployee)?.id || '',
                    employee_name: selectedEmployee,
                    date: today,
                    check_in: currentTime,
                    check_out: null,
                    hours_worked: 0,
                    status: 'present'
                };

                attendance.push(newAttendance);
                showMessage(`تم تسجيل دخول ${selectedEmployee} في ${currentTime}`);
            }

            saveData();
            loadAttendance();
            updateDashboardStats();
        }

        // Populate attendance employee select
        function populateAttendanceSelect() {
            const select = document.getElementById('attendance-employee-select');
            if (select && employees.length > 0) {
                // Clear existing options except the first one
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }

                employees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = `${employee.first_name} ${employee.last_name}`;
                    option.textContent = `${employee.first_name} ${employee.last_name}`;
                    select.appendChild(option);
                });
            }
        }
    </script>
</body>
</html>