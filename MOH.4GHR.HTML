<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية - الكويت</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#2563eb',
              secondary: '#3b82f6',
              accent: '#5b21b6',
              text: {
                light: '#f0f9ff',
                dark: '#1e293b'
              }
            },
            boxShadow: {
              card: '0 4px 20px rgba(0, 0, 0, 0.08)',
              button: '0 4px 12px rgba(37, 99, 235, 0.3)'
            }
          }
        }
      }
    </script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
        }
        
        /* Text animations */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(224, 231, 255, 0.5);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(96, 165, 250, 0.7);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.9);
        }
        
        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: #ffffff;
            margin: auto;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 600px;
            position: relative;
        }
        
        .close-button {
            color: #aaa;
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Dashboard cards */
        .dashboard-card {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        /* Print styling */
        @media print {
            body * {
                visibility: hidden;
            }
            .print-section, .print-section * {
                visibility: visible;
            }
            .print-section {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
        }
        
        /* Sidebar menu for mobile */
        @media (max-width: 768px) {
            #main-nav {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.5s ease;
            }
            
            #main-nav.active {
                max-height: 300px;
            }
        }
        
        /* Fade-in animations */
        .fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
        
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        .smooth-shadow {
            box-shadow: 0 10px 25px rgba(0,0,0,0.06);
        }
    </style>
    <!-- Firebase and HRMS functionality remains the same -->
</head>
<body class="min-h-screen flex flex-col">
    <!-- Header Section -->
    <header class="bg-gradient-to-r from-primary to-accent text-white p-4 sm:p-5 shadow-md rounded-b-xl">
        <div class="container mx-auto flex flex-col sm:flex-row justify-between items-stretch sm:items-center">
            <div class="flex justify-between items-center w-full sm:w-auto">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold flex items-center gap-2">
                        <i class="fas fa-user-friends"></i>
                        نظام إدارة الموارد البشرية
                    </h1>
                    <p class="text-xs sm:text-sm opacity-80 mt-1 flex items-center gap-1">
                        <i class="fas fa-map-marker-alt"></i> الكويت - أخصائي في إدارة الموارد البشرية
                    </p>
                </div>
                <button id="menu-toggle" class="sm:hidden block text-2xl">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav id="main-nav" class="mt-3 sm:mt-0 sm:block">
                <ul class="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 md:gap-5 text-sm sm:text-base">
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-dashboard" data-tab="dashboard-section" class="flex items-center gap-2">
                            <i class="fas fa-home w-5 text-center"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-employees" data-tab="employee-section" class="flex items-center gap-2">
                            <i class="fas fa-users w-5 text-center"></i>
                            الموظفون
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-attendance" data-tab="attendance-section" class="flex items-center gap-2">
                            <i class="fas fa-clock w-5 text-center"></i>
                            الحضور
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-leaves" data-tab="leaves-section" class="flex items-center gap-2">
                            <i class="fas fa-calendar-alt w-5 text-center"></i>
                            الإجازات
                        </a>
                    </li>
                    <li class="rounded-lg hover:bg-white/20 px-3 py-1.5 transition-all">
                        <a href="#" id="nav-salaries" data-tab="salaries-section" class="flex items-center gap-2">
                            <i class="fas fa-wallet w-5 text-center"></i>
                            الرواتب
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="container mx-auto p-4 sm:p-6 flex-grow">
        <div id="message-box" class="hidden"></div>
        
        <!-- User info and quick stats bar -->
        <div class="flex flex-wrap items-center justify-between gap-4 mb-6 bg-white p-4 rounded-xl shadow-sm">
            <div class="flex-1">
                <div class="flex items-center gap-3">
                    <div class="bg-gradient-to-r from-primary to-accent p-2 rounded-full">
                        <i class="fas fa-user-circle text-white text-xl"></i>
                    </div>
                    <div>
                        <p id="user-id-display" class="text-sm text-gray-600"></p>
                        <p class="font-bold">مرحبا بك في نظام إدارة الموارد البشرية</p>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap gap-2">
                <div class="flex items-center gap-2 bg-blue-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-users text-blue-600"></i>
                    <span class="font-bold text-blue-700">الموظفين: <span id="total-employees-count">0</span></span>
                </div>
                <div class="flex items-center gap-2 bg-yellow-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-clock text-yellow-600"></i>
                    <span class="font-bold text-yellow-700">الحضور: <span id="attendance-count">0</span></span>
                </div>
                <div class="flex items-center gap-2 bg-green-50 py-2 px-3 rounded-lg">
                    <i class="fas fa-calendar-alt text-green-600"></i>
                    <span class="font-bold text-green-700">الإجازات: <span id="leaves-count">0</span></span>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 active fade-in">
            <div class="flex justify-between items-center mb-6 border-b-2 pb-4 border-primary">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">لوحة التحكم</h2>
                <div class="text-sm text-gray-500 hidden sm:block">
                    <i class="far fa-calendar-alt ml-2"></i>
                    <span id="current-date">اليوم: 20 ديسمبر 2023</span>
                </div>
            </div>
            
            <!-- Dashboard Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
                <!-- Card: Total Employees -->
                <div class="dashboard-card bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-users text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold">254</p>
                    <p class="text-lg mt-2">إجمالي الموظفين</p>
                    <p class="flex items-center gap-1 mt-2 text-blue-100">
                        <i class="fas fa-arrow-up text-xs"></i>
                        +12 هذا الشهر
                    </p>
                </div>
                
                <!-- Card: Active Employees -->
                <div class="dashboard-card bg-gradient-to-br from-green-500 to-green-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-user-check text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold">224</p>
                    <p class="text-lg mt-2">الموظفين النشطين</p>
                    <p class="flex items-center gap-1 mt-2 text-green-100">
                        94% حضور
                    </p>
                </div>
                
                <!-- Card: Employees on Leave -->
                <div class="dashboard-card bg-gradient-to-br from-yellow-500 to-yellow-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-calendar-alt text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold" id="employees-on-leave-count">22</p>
                    <p class="text-lg mt-2">الموظفون في إجازة</p>
                    <p class="flex items-center gap-1 mt-2 text-yellow-100">
                        +5 هذا الأسبوع
                    </p>
                </div>
                
                <!-- Card: Upcoming Hires -->
                <div class="dashboard-card bg-gradient-to-br from-violet-500 to-violet-600 text-white p-5 sm:p-6 rounded-xl flex flex-col items-center justify-center smooth-shadow">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-3">
                        <i class="fas fa-user-plus text-3xl"></i>
                    </div>
                    <p class="text-3xl sm:text-4xl font-bold" id="upcoming-hires-count">8</p>
                    <p class="text-lg mt-2">توظيفات قادمة</p>
                    <p class="flex items-center gap-1 mt-2 text-violet-100">
                        خلال 3 أشهر
                    </p>
                </div>
            </div>
            
            <!-- Notifications Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div class="border-b border-gray-200">
                    <h3 class="px-5 py-4 text-xl font-semibold text-gray-800">
                        <i class="fas fa-bell text-yellow-500 mr-2"></i>
                        التنبيهات والملاحظات المهمة
                    </h3>
                </div>
                <div id="expiration-notifications" class="p-5 space-y-4">
                    <!-- Expiration notifications will be loaded here -->
                    <div class="p-4 rounded-xl bg-red-50 border border-red-100 animate-fade-in">
                        <div class="flex gap-3">
                            <div class="w-10 h-10 rounded-lg bg-red-500/10 text-red-600 flex items-center justify-center">
                                <i class="fas fa-passport text-lg"></i>
                            </div>
                            <div>
                                <p class="font-medium text-red-700">تاريخ انتهاء جواز السفر</p>
                                <p class="text-sm text-gray-600">جابر يوسف علي | تنتهي في: 2023-12-25 (بعد 5 أيام)</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-4 rounded-xl bg-yellow-50 border border-yellow-100 animate-fade-in">
                        <div class="flex gap-3">
                            <div class="w-10 h-10 rounded-lg bg-yellow-500/10 text-yellow-600 flex items-center justify-center">
                                <i class="fas fa-id-card text-lg"></i>
                            </div>
                            <div>
                                <p class="font-medium text-yellow-700">تاريخ انتهاء رخصة العمل</p>
                                <p class="text-sm text-gray-600">سعود فهد إبراهيم | تنتهي في: 2024-01-15 (بعد 26 يومًا)</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-4 rounded-xl bg-blue-50 border border-blue-100 animate-fade-in">
                        <div class="flex gap-3">
                            <div class="w-10 h-10 rounded-lg bg-blue-500/10 text-blue-600 flex items-center justify-center">
                                <i class="fas fa-calendar-check text-lg"></i>
                            </div>
                            <div>
                                <p class="font-medium text-blue-700">ذكرى سنوية للموظفين</p>
                                <p class="text-sm text-gray-600">لدى ٥ موظفين مرور عام في الشهر القادم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Employee Section -->
        <div id="employee-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b-2 pb-4 border-blue-500">
                <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800">المعلومات الشخصية للموظفين</h2>
                <div class="flex gap-3 flex-wrap">
                    <button id="add-employee-btn" class="bg-primary hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center gap-2">
                        <i class="fas fa-user-plus"></i>
                        إضافة موظف جديد
                    </button>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all flex items-center gap-2">
                        <i class="fas fa-filter"></i>
                        تصفية الموظفين
                    </button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-12 gap-5 mb-6">
                <!-- Search Section -->
                <div class="md:col-span-8 flex">
                    <input type="text" placeholder="بحث الموظفين بالاسم أو الرقم" class="flex-grow border-l-0 rounded-l-lg border border-gray-300 px-4 py-2 focus:outline-none focus:border-primary">
                    <button class="bg-primary text-white px-5 rounded-r-lg">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <!-- Actions Section -->
                <div class="md:col-span-4 flex gap-2 justify-end">
                    <button class="bg-white border border-gray-300 rounded-lg px-4 h-full hover:bg-gray-50 flex items-center gap-2">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button class="bg-white border border-gray-300 rounded-lg px-4 h-full hover:bg-gray-50 flex items-center gap-2">
                        <i class="fas fa-file-export"></i>
                        تصدير
                    </button>
                </div>
            </div>
            
            <!-- Employee List -->
            <div id="employee-list" class="space-y-4">
                <!-- Employee Card 1 -->
                <div class="employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md">
                    <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                        <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                    </div>
                    <div class="p-4 sm:p-6 flex-grow">
                        <div class="flex flex-wrap justify-between">
                            <h3 class="text-xl font-bold text-gray-800">طارق عبدالله خالد</h3>
                            <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">نشيط</span>
                        </div>
                        
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-id-card text-gray-400 w-5"></i>
                                <span class="font-medium">الرقم المدني: 293012301230</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                                <span>+965 50012345</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-building text-gray-400 w-5"></i>
                                <span>الأقسام: تقنية المعلومات</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-briefcase text-gray-400 w-5"></i>
                                <span>المسمى الوظيفي: مطور ويب</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-4">
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-user-circle text-blue-600"></i>
                                عرض الملف الشخصي
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-envelope text-yellow-600"></i>
                                إرسال رسالة
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-money-bill-wave text-green-600"></i>
                                الرواتب
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Card 2 -->
                <div class="employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md">
                    <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                        <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                    </div>
                    <div class="p-4 sm:p-6 flex-grow">
                        <div class="flex flex-wrap justify-between">
                            <h3 class="text-xl font-bold text-gray-800">نورة أحمد سعد</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">في إجازة</span>
                        </div>
                        
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-id-card text-gray-400 w-5"></i>
                                <span class="font-medium">الرقم المدني: 293082301230</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                                <span>+965 50054321</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-building text-gray-400 w-5"></i>
                                <span>الأقسام: الموارد البشرية</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-briefcase text-gray-400 w-5"></i>
                                <span>المسمى الوظيفي: أخصائي شؤون الموظفين</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mt-4">
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-user-circle text-blue-600"></i>
                                عرض الملف الشخصي
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-envelope text-yellow-600"></i>
                                إرسال رسالة
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                                <i class="fas fa-calendar-alt text-purple-600"></i>
                                حالة الإجازة
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Loading indicator -->
                <p class="text-gray-600 text-center text-lg py-8 animate-pulse italic">
                    جاري تحميل بيانات الموظفين...
                </p>
            </div>
        </div>
        
        <!-- Attendance Section -->
        <div id="attendance-section" class="tab-content bg-white rounded-2xl shadow-lg p-5 sm:p-8 fade-in">
            <h2 class="text-2xl sm:text-3xl font-semibold text-gray-800 mb-6 border-b-2 pb-4 border-primary">الحضور والانصراف</h2>
            
            <!-- Attendance Today Stats -->
            <div class="flex flex-col md:flex-row gap-5 mb-8">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-xl w-full md:w-1/2">
                    <div class="flex justify-between">
                        <div>
                            <p class="text-sm opacity-90">حضور اليوم</p>
                            <p class="text-3xl font-bold mt-1">189 <span class="text-lg font-medium">/ 230</span></p>
                            <p class="text-sm mt-2 flex items-center gap-1">89% الحضور</p>
                        </div>
                        <div class="bg-white/20 p-4 rounded-full">
                            <i class="fas fa-user-clock text-3xl"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="h-2 bg-white/40 rounded-full">
                            <div class="h-2 bg-white rounded-full" style="width: 89%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 border rounded-xl p-6 w-full md:w-1/2">
                    <p class="text-lg font-semibold mb-3">تسجيل الدخول السريع</p>
                    <div class="bg-blue-50 border border-blue-100 rounded-xl p-4">
                        <div class="flex flex-col sm:flex-row sm:items-center gap-3 mb-3">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">اختر الموظف:</label>
                                <select id="attendance-employee-select" class="w-full border rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary/50">
                                    <option>-- اختر الموظف --</option>
                                    <option>طارق عبدالله خالد</option>
                                    <option>نورة أحمد سعد</option>
                                    <option>فهد عبدالعزيز محمد</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button id="clock-in-out-btn" class="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-600 text-white font-medium py-2 px-5 rounded-lg transition duration-300 shadow-md w-full sm:w-auto">
                                <i class="fas fa-clock"></i>
                                تسجيل الدخول / الخروج
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Records -->
            <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <div class="border-b border-gray-200 px-5 py-4 flex justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">سجلات الحضور</h3>
                    <div>
                        <select id="month-select" class="text-sm border p-2 rounded">
                            <option>ديسمبر 2023</option>
                            <option selected>نوفمبر 2023</option>
                            <option>أكتوبر 2023</option>
                        </select>
                    </div>
                </div>
                <div id="attendance-list" class="p-4 sm:p-5">
                    <ul class="divide-y divide-gray-100">
                        <li class="py-4">
                            <div class="flex flex-col sm:flex-row sm:items-center">
                                <div class="flex-1 mb-3 sm:mb-0">
                                    <h4 class="font-medium text-gray-800">طارق عبدالله خالد</h4>
                                    <p class="text-sm text-gray-500 mt-1">الأقسام: تقنية المعلومات</p>
                                </div>
                                <div class="flex items-center gap-4 sm:gap-8">
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الدخول</p>
                                        <p class="font-medium text-green-600 mt-0.5">08:45 صباحًا</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الخروج</p>
                                        <p class="font-medium text-red-600 mt-0.5">05:12 مساءً</p>
                                    </div>
                                    <div class="bg-blue-50 px-3 py-1 rounded-full">
                                        <span class="text-blue-600 text-sm">8 ساعات 27 د</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        <li class="py-4">
                            <div class="flex flex-col sm:flex-row sm:items-center">
                                <div class="flex-1 mb-3 sm:mb-0">
                                    <h4 class="font-medium text-gray-800">نورة أحمد سعد</h4>
                                    <p class="text-sm text-gray-500 mt-1">الأقسام: الموارد البشرية</p>
                                </div>
                                <div class="flex items-center gap-4 sm:gap-8">
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الدخول</p>
                                        <p class="font-medium text-green-600 mt-0.5">08:32 صباحًا</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 text-sm">وقت الخروج</p>
                                        <p class="font-medium text-gray-400 mt-0.5">--</p>
                                    </div>
                                    <div class="bg-yellow-50 px-3 py-1 rounded-full">
                                        <span class="text-yellow-600 text-sm">لا تزال موجودة</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <p class="text-gray-600 text-center py-5 italic">
                        جارٍ تحميل المزيد من سجلات الحضور...
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Other sections (Leaves & Salaries) structure would be similar -->
        <!-- Placeholder for other sections -->
    </main>
    
    <!-- Modals section would be here just as in the original -->
    <!-- Placeholder for modals section -->

    <!-- Footer Section -->
    <footer class="bg-primary text-white mt-8 pt-8 pb-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between gap-8">
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">نظام إدارة الموارد البشريّة</h3>
                    <p class="text-sm opacity-80 leading-relaxed">
                        نظام متكامل لإدارة الموارد البشرية للشركات في دولة الكويت، يشمل نظام الحضور، الرواتب، الإجازات، وشؤون ومعلومات الموظفين.
                    </p>
                </div>
                
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">روابط سريعة</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">لوحة التحكم</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الموظفون</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الحضور والانصراف</a></li>
                        <li><a href="#" class="text-sm opacity-80 hover:opacity-100">الإجازات</a></li>
                    </ul>
                </div>
                
                <div class="md:w-1/4">
                    <h3 class="font-bold text-lg mb-4">الدعم الفني</h3>
                    <ul class="space-y-3">
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-map-marker-alt mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80">مدينة الكويت - برج التحرير</span>
                        </li>
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-phone-alt mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80">+965 22212345</span>
                        </li>
                        <li class="flex gap-3 items-start">
                            <i class="fas fa-envelope mt-1 text-lg w-5 opacity-80"></i>
                            <span class="text-sm opacity-80"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t mt-8 pt-6 text-center text-sm opacity-80">
                <p>&copy; 2023 نظام إدارة الموارد البشريّّة - جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
    
    <script>
        // Toggle mobile menu
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const nav = document.getElementById('main-nav');
            nav.classList.toggle('hidden');
            if(!nav.classList.contains('hidden')) {
                nav.classList.add('block', 'py-3', 'px-3', 'bg-white/10', 'rounded-lg', 'mt-2');
            }
        });
        
        // Set current date
        const date = new Date();
        const formattedDate = date.toLocaleDateString('ar-KW', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('current-date').textContent = 'اليوم: ' + formattedDate;
        
        // Simulate tab switching
        document.querySelectorAll('nav ul li a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                // Remove active class from all
                document.querySelectorAll('nav ul li a').forEach(el => {
                    el.parentElement.classList.remove('bg-white/20');
                    el.classList.remove('font-bold', 'text-blue-100');
                });
                // Add active state
                this.classList.add('font-bold', 'text-blue-100');
                this.parentElement.classList.add('bg-white/20');
            });
        });
        
        // Active dashboard nav item by default
        document.querySelector('[data-tab="dashboard-section"]').classList.add('font-bold', 'text-blue-100');
        document.querySelector('[data-tab="dashboard-section"]').parentElement.classList.add('bg-white/20');
        
        // Modal example functionality
        document.getElementById('add-employee-btn').addEventListener('click', function() {
            document.getElementById('employee-form').reset();
            document.getElementById('add-employee-modal').style.display = 'flex';
        });
        
        // Close modals
        document.querySelectorAll('.close-button').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });
        
        // Function to show message
        function showMessage(message, type = 'success') {
            const messageBox = document.getElementById('message-box');
            messageBox.textContent = message;
            messageBox.className = `p-3 rounded-lg text-white mb-4 text-center animate-fade-in ${type === 'error' ? 'bg-red-500' : 'bg-green-500'}`;
            messageBox.style.display = 'block';
            
            setTimeout(() => {
                messageBox.style.display = 'none';
            }, 5000);
        }

        // Initialize Firebase
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Firebase config (replace with your actual config)
                const firebaseConfig = {
                    apiKey: "your-api-key",
                    authDomain: "your-project.firebaseapp.com",
                    projectId: "your-project-id",
                    storageBucket: "your-bucket.appspot.com",
                    messagingSenderId: "your-sender-id",
                    appId: "your-app-id"
                };
                
                app = initializeApp(firebaseConfig);
                db = getFirestore(app);
                auth = getAuth(app);
                
                // Sign in anonymously
                await signInAnonymously(auth);
                
                // Listen for auth state changes
                onAuthStateChanged(auth, (user) => {
                    if (user) {
                        userId = user.uid;
                        document.getElementById('user-id-display').textContent = `معرف المستخدم: ${userId}`;
                        getEmployees(); // Fetch employees after auth
                    }
                });
                
                // Show dashboard by default
                showTab('dashboard-section');
                
            } catch (error) {
                console.error("Firebase initialization error:", error);
                showMessage(`حدث خطأ في تهيئة النظام: ${error.message}`, 'error');
            }

            // Add Employee Form Submission
            const employeeForm = document.getElementById('employee-form');
            if (employeeForm) {
                employeeForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const employeeData = {
                        first_name: document.getElementById('first-name').value,
                        last_name: document.getElementById('last-name').value,
                        email: document.getElementById('email').value,
                        file_number: document.getElementById('file-number').value,
                        civil_id: document.getElementById('civil-id').value,
                        department: document.getElementById('department').value,
                        position: document.getElementById('position').value,
                        hire_date: document.getElementById('hire-date').value,
                        residence_expiry_date: document.getElementById('residence-expiry-date').value,
                        passport_expiry_date: document.getElementById('passport-expiry-date').value,
                        personal_phone_number: document.getElementById('personal-phone-number').value,
                        work_phone_number: document.getElementById('work-phone-number').value,
                        license_expiry_date: document.getElementById('license-expiry-date').value,
                        health_card_expiry_date: document.getElementById('health-card-expiry-date').value,
                        status: 'active',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };

                    try {
                        if (employeeId) {
                            // Update existing employee
                            await updateDoc(doc(db, `artifacts/${app.config.projectId}/users/${userId}/employees`, employeeId), employeeData);
                            showMessage('تم تحديث بيانات الموظف بنجاح');
                        } else {
                            // Add new employee
                            await addDoc(collection(db, `artifacts/${app.config.projectId}/users/${userId}/employees`), employeeData);
                            showMessage('تم إضافة الموظف بنجاح');
                        }
                        
                        // Close modal and refresh list
                        document.getElementById('add-employee-modal').style.display = 'none';
                        getEmployees();
                        
                    } catch (error) {
                        console.error("Error saving employee:", error);
                        showMessage(`حدث خطأ أثناء حفظ البيانات: ${error.message}`, 'error');
                    }
                });
            }

            // Add Employee Button
            document.getElementById('add-employee-btn').addEventListener('click', () => {
                employeeId = '';
                document.getElementById('employee-id-field').value = '';
                document.getElementById('form-title').textContent = 'إضافة موظف جديد';
                document.getElementById('employee-form').reset();
                document.getElementById('add-employee-modal').style.display = 'flex';
            });
        });

        // Function to get employees
        function getEmployees() {
            if (userId === 'loading...') return;

            const employeeListDiv = document.getElementById('employee-list');
            if (!employeeListDiv) return;

            onSnapshot(collection(db, `artifacts/${app.config.projectId}/users/${userId}/employees`), (snapshot) => {
                employeeListDiv.innerHTML = ''; // Clear current list

                if (snapshot.empty) {
                    employeeListDiv.innerHTML = `
                        <div class="bg-gray-50 p-8 rounded-xl text-center">
                            <i class="fas fa-users-slash text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">لا يوجد موظفون مسجلون بعد</p>
                        </div>
                    `;
                    return;
                }

                snapshot.forEach(doc => {
                    const employee = doc.data();
                    employeeListDiv.appendChild(createEmployeeCard(doc.id, employee));
                });

                // Update dashboard stats
                updateDashboardStats(snapshot.size);
            });
        }

        // Function to create employee card
        function createEmployeeCard(id, employee) {
            const card = document.createElement('div');
            card.className = 'employee-card rounded-xl bg-white transition-all duration-300 sm:flex items-start border border-gray-100 shadow-sm overflow-hidden hover:shadow-md';
            card.innerHTML = `
                <div class="bg-gray-100 p-4 w-full sm:w-48 flex items-center justify-center">
                    <div class="bg-gray-300 border-2 border-dashed rounded-xl w-16 h-16" />
                </div>
                <div class="p-4 sm:p-6 flex-grow">
                    <div class="flex flex-wrap justify-between">
                        <h3 class="text-xl font-bold text-gray-800">${employee.first_name || ''} ${employee.last_name || ''}</h3>
                        <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">${employee.status === 'active' ? 'نشيط' : 'غير نشط'}</span>
                    </div>
                    
                    <div class="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-600">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-id-card text-gray-400 w-5"></i>
                            <span class="font-medium">الرقم المدني: ${employee.civil_id || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-phone-alt text-gray-400 w-5"></i>
                            <span>${employee.personal_phone_number || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-building text-gray-400 w-5"></i>
                            <span>الأقسام: ${employee.department || 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-briefcase text-gray-400 w-5"></i>
                            <span>المسمى الوظيفي: ${employee.position || 'غير محدد'}</span>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mt-4">
                        <button onclick="viewEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-user-circle text-blue-600"></i>
                            عرض الملف الشخصي
                        </button>
                        <button onclick="editEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-edit text-yellow-600"></i>
                            تعديل البيانات
                        </button>
                        <button onclick="deleteEmployee('${id}')" class="bg-gray-100 hover:bg-gray-200 py-2 px-3 rounded-lg text-sm flex items-center gap-2 transition">
                            <i class="fas fa-trash-alt text-red-600"></i>
                            حذف الموظف
                        </button>
                    </div>
                </div>
            `;
            return card;
        }

        // Function to edit employee
        window.editEmployee = async function(id) {
            employeeId = id;
            const docRef = doc(db, `artifacts/${app.config.projectId}/users/${userId}/employees`, id);
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
                const employee = docSnap.data();
                document.getElementById('form-title').textContent = 'تعديل بيانات الموظف';
                document.getElementById('employee-id-field').value = id;
                
                // Set form values
                document.getElementById('first-name').value = employee.first_name || '';
                document.getElementById('last-name').value = employee.last_name || '';
                document.getElementById('email').value = employee.email || '';
                document.getElementById('file-number').value = employee.file_number || '';
                document.getElementById('civil-id').value = employee.civil_id || '';
                document.getElementById('department').value = employee.department || '';
                document.getElementById('position').value = employee.position || '';
                document.getElementById('hire-date').value = employee.hire_date || '';
                document.getElementById('residence-expiry-date').value = employee.residence_expiry_date || '';
                document.getElementById('passport-expiry-date').value = employee.passport_expiry_date || '';
                document.getElementById('license-expiry-date').value = employee.license_expiry_date || '';
                document.getElementById('health-card-expiry-date').value = employee.health_card_expiry_date || '';
                document.getElementById('personal-phone-number').value = employee.personal_phone_number || '';
                document.getElementById('work-phone-number').value = employee.work_phone_number || '';
                
                document.getElementById('add-employee-modal').style.display = 'flex';
            } else {
                showMessage('لم يتم العثور على بيانات الموظف', 'error');
            }
        };

        // Function to delete employee
        window.deleteEmployee = async function(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                try {
                    await deleteDoc(doc(db, `artifacts/${app.config.projectId}/users/${userId}/employees`, id));
                    showMessage('تم حذف الموظف بنجاح');
                    getEmployees();
                } catch (error) {
                    showMessage('حدث خطأ أثناء حذف الموظف', 'error');
                }
            }
        };

        // Function to update dashboard stats
        function updateDashboardStats(totalEmployees) {
            document.getElementById('total-employees-count').textContent = totalEmployees;
            // Update other stats as needed
        }

        // Function to switch tabs
        function showTab(tabId) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.getElementById(tabId).classList.add('active');
        }
    </script>
</body>
</html>